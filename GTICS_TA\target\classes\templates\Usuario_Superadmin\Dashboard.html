<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="">
    <meta name="author" content="">
    <link rel="icon" href="/images/logo-solo.png">

    <title>Superadmin</title>

    <!-- Vendors Style-->
    <link rel="stylesheet" href="/css/vendors_css.css">

    <!-- Style-->
    <link rel="stylesheet" href="/css/style.css">
    <link rel="stylesheet" href="/css/skin_color.css">
</head>
<body class="hold-transition light-skin sidebar-mini theme-success fixed">


<div class="wrapper">
    <div id="loader"></div>

    <header class="main-header">
        <div class="d-flex align-items-center logo-box justify-content-center">
            <!-- Logo -->
            <a href="principal.html" class="logo">
                <!-- logo-->
                <div class="logo-mini w-150 text-center">
                    <span class="light-logo"><img src="/images/logo-sanMiguel.png" alt="logo"></span>
                </div>
            </a>
        </div>
        <!-- Header Navbar -->
        <nav class="navbar navbar-static-top">
            <!-- Sidebar toggle button-->
            <div class="app-menu">
                <ul class="header-megamenu nav">
                    <li class="btn-group nav-item">
                        <a href="#" class="waves-effect waves-light nav-link push-btn btn-primary-light" data-toggle="push-menu" role="button">
                            <i data-feather="align-left"></i>
                        </a>
                    </li>

                </ul>
            </div>

            <div class="navbar-custom-menu r-side">
                <ul class="nav navbar-nav">
                    <li class="btn-group nav-item d-lg-inline-flex d-none">
                        <a href="#" data-provide="fullscreen" class="waves-effect waves-light nav-link full-screen btn-warning-light" title="Full Screen">
                            <i data-feather="maximize"></i>
                        </a>
                    </li>
                    <!-- Notifications -->
                    <li class="dropdown notifications-menu">
                        <ul class="dropdown-menu animated bounceIn">

                            <li class="header">
                                <div class="p-20">
                                    <div class="flexbox">

                                        <div>
                                            <a href="#" class="text-danger">Clear All</a>
                                        </div>
                                    </div>
                                </div>
                            </li>

                            <li>
                                <!-- inner menu: contains the actual data -->
                                <ul class="menu sm-scrol">
                                    <li>
                                        <a href="#">
                                            <i class="fa fa-users text-info"></i> Curabitur id eros quis nunc suscipit blandit.
                                        </a>
                                    </li>
                                    <li>
                                        <a href="#">
                                            <i class="fa fa-warning text-warning"></i> Duis malesuada justo eu sapien elementum, in semper diam posuere.
                                        </a>
                                    </li>
                                    <li>
                                        <a href="#">
                                            <i class="fa fa-users text-danger"></i> Donec at nisi sit amet tortor commodo porttitor pretium a erat.
                                        </a>
                                    </li>
                                    <li>
                                        <a href="#">
                                            <i class="fa fa-shopping-cart text-success"></i> In gravida mauris et nisi
                                        </a>
                                    </li>
                                    <li>
                                        <a href="#">
                                            <i class="fa fa-user text-danger"></i> Praesent eu lacus in libero dictum fermentum.
                                        </a>
                                    </li>
                                    <li>
                                        <a href="#">
                                            <i class="fa fa-user text-primary"></i> Nunc fringilla lorem
                                        </a>
                                    </li>
                                    <li>
                                        <a href="#">
                                            <i class="fa fa-user text-success"></i> Nullam euismod dolor ut quam interdum, at scelerisque ipsum imperdiet.
                                        </a>
                                    </li>
                                </ul>
                            </li>
                            <li class="footer">
                                <a href="#">View all</a>
                            </li>
                        </ul>
                    </li>


                    <!-- User Account-->
                    <li class="dropdown user user-menu">
                        <a href="#" class="waves-effect waves-light dropdown-toggle w-auto l-h-12 bg-transparent py-0 no-shadow" data-bs-toggle="dropdown" title="User">
                            <div class="d-flex pt-5">
                            </div>
                        </a>
                        <ul class="dropdown-menu animated flipInX">
                            <li class="user-body">
                                <a class="dropdown-item" href="#"><i class="ti-user text-muted me-2"></i> Profile</a>
                                <a class="dropdown-item" href="#"><i class="ti-wallet text-muted me-2"></i> My Wallet</a>
                                <a class="dropdown-item" href="#"><i class="ti-settings text-muted me-2"></i> Settings</a>
                                <div class="dropdown-divider"></div>
                                <a class="dropdown-item" href="#"><i class="ti-lock text-muted me-2"></i> Logout</a>
                            </li>
                        </ul>
                    </li>

                </ul>
            </div>
        </nav>
    </header>
    <aside class="main-sidebar">
        <!-- sidebar-->
        <section class="sidebar position-relative">

            <div class="multinav">
                <div class="multinav-scroll" style="height: 100%;">
                    <!-- sidebar menu-->
                    <ul class="sidebar-menu" data-widget="tree">
                        <li>
                            <a href="/SuperAdmin">
                                <i data-feather="monitor"></i>
                                <span>Dashboard</span>

                            </a>

                        </li>
                        <li>
                            <a href="/SuperAdmin/usuarios-baneados">
                                <i data-feather="calendar"></i>
                                <span>Baneos</span>
                            </a>
                        </li>
                        <li >
                            <a href="/SuperAdmin/usuarios-no-baneados">
                                <i data-feather="users"></i>
                                <span>Usuarios</span>

                            </a>
                        </li>
                        <li>
                            <a href="#" class="nav-link" onclick="document.getElementById('logoutForm2').submit(); return false;">
                                <i data-feather="log-out"></i>
                                <span>Cerrar sesión</span>
                            </a>
                            <form id="logoutForm2" th:action="@{/logout}" method="post" style="display: none;"></form>
                        </li>
                    </ul>
                </div>
            </div>
        </section>
    </aside>
    <!-- Left side column. contains the logo and sidebar -->
    <div class="content-wrapper">
        <div class="container-full">
            <!-- Main content -->
            <section class="content">

                <div class="row">
                    <!-- Card 1 -->
                    <div class="col-md-3 col-sm-6 col-12 mb-3">
                        <div class="card shadow-sm border-0 text-center p-3 h-140" style="background-color: #D1FAE5;">
                            <div class="bg-primary-light rounded10 p-20 mx-auto w-100 h-100">
                                <img src="/images/svg-icon/medical/icono_1_d.svg" class="" alt="" />
                            </div>
                            <h6 class="text-dark">Total de Usuarios</h6>
                            <h2 class="text-success fw-bold" th:text="${dashboard.totalUsuarios}">0</h2>
                        </div>
                    </div>
                    <!-- Card 2 -->
                    <div class="col-md-3 col-sm-6 col-12 mb-3">
                        <div class="card shadow-sm border-0 text-center p-3 h-140" style="background-color: #FECACA;">
                                <div class="bg-danger-light rounded10 p-20 mx-auto w-100 h-100">
                                    <img src="/images/svg-icon/medical/usuarios_baneados.svg" class="" alt="" />
                                </div>
                                <h6 class="text-dark">Total de Usuarios Baneados</h6>
                                <h2 class="text-success fw-bold" th:text="${dashboard.totalUsuariosBaneados}">0</h2>
                        </div>
                    </div>

                    <!-- Card 3 -->
                    <div class="col-md-3 col-sm-6 col-12 mb-3">
                    <div class="card shadow-sm border-0 text-center p-3 h-140" style="background-color: #BFDBFE;">
                        <div class="bg-warning-light rounded10 p-20 mx-auto w-100 h-100">
                            <img src="/images/svg-icon/medical/espacio_deportivo.svg" class="" alt="" />
                        </div>
                        <h6 class="text-dark">Cantidad de espacios </h6>
                        <h2 class="text-success fw-bold" th:text="${dashboard.espaciosDisponibles}">0</h2>
                    </div>
                </div>

                    <div class="col-md-3 col-sm-6 col-12 mb-3">
                        <div class="card shadow-sm border-0 text-center p-3 h-140" style="background-color: #E0E7FF;">
                                <div class="bg-info-light rounded10 p-20 mx-auto w-100 h-100">
                                    <img src="/images/svg-icon/medical/espacio_deportivo_ocupado.svg" class="" alt="" />
                                </div>
                                <h6 class="text-dark">Cantidad de reservas</h6>
                            <h2 class="text-success fw-bold" th:text="${dashboard.cantidadTotalReservas}">0</h2>
                        </div>
                    </div>

                <!-- Una sola fila para ambos gráficos -->
                <div class="row">
                    <!-- Primer gráfico de barras -->
                    <div class="col-xl-6 col-12">
                        <div class="box">
                            <div class="box-header">
                                <h4 class="box-title">Reporte Económico de los servicios vs cachas reservadas en los últimos 3 meses</h4>
                            </div>
                            <div class="box-body">
                                <h3 class="text-primary mt-0" th:text="'S/ ' + ${dashboard.totalRecaudadoUltimos3Meses}">S/ 0.00</h3>
                                <small class="text-muted" th:text="${dashboard.reservasUltimos3Meses} + ' reservas'">0 reservas</small>
                                <div id="recent_trend_1"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Segundo gráfico de barras -->
                    <div class="col-xl-6 col-12">
                        <div class="box">
                            <div class="box-header">
                                <h4 class="box-title">Reporte Económico de los servicios vs cachas reservadas en el año</h4>
                            </div>
                            <div class="box-body">
                                <h3 class="text-primary mt-0" th:text="'S/ ' + ${dashboard.totalRecaudadoAnual}">S/ 0.00</h3>
                                <small class="text-muted" th:text="${dashboard.reservasAnuales} + ' reservas'">0 reservas</small>
                                <div id="recent_trend_2"></div>
                            </div>

                        </div>
                    </div>
                </div>

                <!-- Segunda fila con el gráfico circular y las estadísticas de pacientes alineados -->
                <div class="row">
                    <!-- Gráfico de barras horizontal: Top 10 Servicios Más Reservados -->
                    <div class="col-xl-6 col-12">
                        <div class="box">
                            <div class="box-header">
                                <h4 class="box-title">Top 10 Servicios Más Reservados</h4>
                            </div>
                            <div class="box-body">
                                <div id="top_services"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Gráfico circular: Porcentaje de Servicios Reservados -->
                    <div class="col-xl-6 col-12">
                        <div class="box">
                            <div class="box-header">
                                <h4 class="box-title">Porcentaje de Servicios Reservados</h4>
                            </div>
                            <div class="box-body">
                                <div id="chart432"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div> <!-- cierre de div.container-full -->
        </div>
    </div><!-- cierre de div.content-wrapper -->


<!-- /.content-wrapper -->
<footer class="main-footer">
    <div class="pull-right d-none d-sm-inline-block">
        <ul class="nav nav-primary nav-dotted nav-dot-separated justify-content-center justify-content-md-end">


        </ul>
    </div>
</footer>


<script src="/js/vendors.min.js"></script>
<script src="/js/pages/chat-popup.js"></script>
<script src="/assets/icons/feather-icons/feather.min.js"></script>
<script src="/assets/vendor_components/apexcharts-bundle/dist/apexcharts.js"></script>
<script src="/assets/vendor_components/date-paginator/moment.min.js"></script>
<script src="/assets/vendor_components/bootstrap-datepicker/dist/js/bootstrap-datepicker.min.js"></script>
<script src="/assets/vendor_components/date-paginator/bootstrap-datepaginator.min.js"></script>

<!-- Rhythm Admin App -->
<script src="/js/template.js"></script>
<script src="/js/pages/dashboard.js"></script>
<script th:inline="javascript">
    document.addEventListener("DOMContentLoaded", function () {
        let meses = /*[[${dashboard.mesesUltimos3Meses}]]*/ [];
        let totales = /*[[${dashboard.recaudacionUltimos3Meses}]]*/ [];
        let reservas = /*[[${dashboard.reservasUltimos3MesesLista}]]*/ [];

        const options1 = {
            series: [
                { name: 'Recaudación (S/)', data: totales },
                { name: 'Reservas', data: reservas }
            ],
            chart: {
                type: 'line',
                height: 300,
                toolbar: {
                    show: true,
                    tools: {
                        download: true,
                        selection: false,
                        zoom: false,
                        zoomin: false,
                        zoomout: false,
                        pan: false,
                        reset: false
                    },
                    export: {
                        csv: {
                            filename: "reporte_3_meses",
                            columnDelimiter: ",",
                            headerCategory: "Mes",
                            headerValue: "Valor",
                            dateFormatter(timestamp) {
                                return new Date(timestamp).toLocaleDateString();
                            }
                        },
                        svg: { filename: "reporte_3_meses" },
                        png: { filename: "reporte_3_meses" }
                    }
                }
            },
            stroke: {
                curve: 'straight',
                width: 2
            },
            markers: {
                size: 4
            },
            xaxis: {
                categories: meses,
                labels: {
                    rotate: -45
                }
            },
            colors: ['#00D0FF', '#28C76F'],
            tooltip: {
                shared: true,
                intersect: false,
                y: {
                    formatter: function (val, { seriesIndex, w }) {
                        const label = w.globals.seriesNames[seriesIndex];
                        if (label.includes("Recaudación")) {
                            return "S/ " + val.toFixed(2);
                        } else if (label.includes("Reservas")) {
                            return val + " reservas";
                        } else {
                            return val;
                        }
                    }
                }
            },
            legend: {
                show: true
            }
        };


        const chart1 = new ApexCharts(document.querySelector("#recent_trend_1"), options1);
        chart1.render();

        // Segundo gráfico
        let mesesAnual = /*[[${dashboard.mesesAnuales}]]*/ [];
        let totalesAnual = /*[[${dashboard.recaudacionAnualPorMes}]]*/ [];
        let reservasAnual = /*[[${dashboard.reservasAnualesPorMes}]]*/ [];

        const options2 = {
            series: [
                { name: 'Recaudación (S/)', data: totalesAnual },
                { name: 'Reservas', data: reservasAnual }
            ],
            chart: {
                type: 'line',
                height: 300,
                toolbar: {
                    show: true,
                    tools: {
                        download: true,
                        selection: false,
                        zoom: false,
                        zoomin: false,
                        zoomout: false,
                        pan: false,
                        reset: false,
                        customIcons: []
                    },
                    export: {
                        csv: {
                            filename: "reporte_anual",
                            columnDelimiter: ",",
                            headerCategory: "Mes",
                            headerValue: "Valor",
                            dateFormatter(timestamp) {
                                return new Date(timestamp).toLocaleDateString();
                            }
                        },
                        svg: {
                            filename: "reporte_anual"
                        },
                        png: {
                            filename: "reporte_anual"
                        }
                    }
                }
            },
            stroke: {
                curve: 'straight',
                width: 2
            },
            markers: {
                size: 4
            },
            xaxis: {
                categories: mesesAnual,
                labels: {
                    rotate: -45
                }
            },
            tooltip: {
                shared: false, // Desactivamos el comportamiento combinado
                intersect: true,
                y: {
                    formatter: function (val, { series, seriesIndex, dataPointIndex, w }) {
                        const label = w.globals.seriesNames[seriesIndex];
                        if (label.includes("Recaudación")) {
                            return "S/ " + val.toFixed(2);
                        } else if (label.includes("Reservas")) {
                            return val + " reservas";
                        } else {
                            return val;
                        }
                    }
                }
            },
            colors: ['#00D0FF', '#28C76F'],
            legend: { show: true }
        };

        const chart2 = new ApexCharts(document.querySelector("#recent_trend_2"), options2);
        chart2.render();
    });
</script>
<script th:inline="javascript">
    document.addEventListener("DOMContentLoaded", function () {

        const nombresTop = /*[[${dashboard.nombresServiciosTop}]]*/ [];
        const cantidadesTop = /*[[${dashboard.cantidadReservasTop}]]*/ [];

        const topServicesChart = new ApexCharts(document.querySelector("#top_services"), {
            series: [{
                name: 'Reservas',
                data: cantidadesTop
            }],
            chart: {
                type: 'bar',
                height: Math.max(355, cantidadesTop.length * 10) // 45 px por barra
            },
            plotOptions: {
                bar: {
                    horizontal: true,
                    endingShape: 'rounded'
                }
            },
            xaxis: {
                categories: nombresTop,
                labels: {
                    style: {
                        fontSize: '13px',
                        fontFamily: 'inherit',
                        cssClass: 'apexcharts-xaxis-label'
                    }
                }
            },
            colors: ['#00D0FF']
        });
        topServicesChart.render();

        const nombresPorcentaje = /*[[${dashboard.nombresServiciosPorcentaje}]]*/ [];
        const cantidadesPorcentaje = /*[[${dashboard.cantidadServiciosPorcentaje}]]*/ [];

        const porcentajeChart = new ApexCharts(document.querySelector("#chart432"), {
            series: cantidadesPorcentaje,
            chart: {
                type: 'donut',
                height: 350,
                toolbar: {
                    show: true,
                    tools: {
                        download: true,
                        selection: false,
                        zoom: false,
                        zoomin: false,
                        zoomout: false,
                        pan: false,
                        reset: false
                    },
                    export: {
                        csv: {
                            filename: "porcentaje_servicios",
                            headerCategory: "Servicio",
                            headerValue: "Reservas"
                        },
                        svg: {
                            filename: "porcentaje_servicios"
                        },
                        png: {
                            filename: "porcentaje_servicios"
                        }
                    }
                }
            },
            labels: nombresPorcentaje,
            plotOptions: {
                pie: {
                    donut: {
                        size: '65%',
                        labels: {
                            show: true,
                            total: {
                                show: true,
                                label: 'Total',
                                formatter: function (w) {
                                    return w.globals.seriesTotals.reduce((a, b) => a + b, 0) + " reservas";
                                }
                            }
                        }
                    }
                }
            },
            legend: {
                position: 'right'
            },
            tooltip: {
                y: {
                    formatter: function (value, { series }) {
                        const total = series.reduce((a, b) => a + b, 0);
                        return `${value} reservas (${((value / total) * 100).toFixed(1)}%)`;
                    }
                }
            }
        });
        porcentajeChart.render();

    });
</script>

</body>
</html>