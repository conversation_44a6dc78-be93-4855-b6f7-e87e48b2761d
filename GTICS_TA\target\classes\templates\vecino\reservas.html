<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
    <head>
        <meta charset="utf-8">
          <meta http-equiv="X-UA-Compatible" content="IE=edge">
          <meta name="viewport" content="width=device-width, initial-scale=1">
          <meta name="description" content="">
          <meta name="author" content="">
          <link rel="icon" href="/images/san_miguel_logo.ico">
      
          <title>Mis Reservas</title>
        
          <!-- Vendors Style-->
          <link rel="stylesheet" href="/css/vendors_css.css">
            
          <!-- Style-->  
          <link rel="stylesheet" href="/css/style.css">
          <link rel="stylesheet" href="/css/skin_color.css">


	</head>

<body class="hold-transition light-skin sidebar-mini theme-success fixed">
    
<div class="wrapper">
	<div id="loader"></div>

	<header class="main-header">
		<div class="d-flex align-items-center logo-box justify-content-center">
			<!-- Logo -->
			<a href="/static/assets/vendor_components/jquery-validation-1.17.0/demo/requirejs/index.html" class="logo">
				<!-- logo-->
				<div class="logo-mini w-150 text-center">
					<span class="light-logo"><img th:src="@{/images/logo-sanMiguel.png}" alt="logo"></span>
				</div>
			</a>
		</div>
		<!-- Header Navbar -->
		<nav class="navbar navbar-static-top">
		  <!-- Sidebar toggle button-->
			<div class="app-menu">
				<ul class="header-megamenu nav">
					<li class="btn-group nav-item">
						<a href="#" class="waves-effect waves-light nav-link push-btn btn-primary-light" data-toggle="push-menu" role="button">
							<i data-feather="align-left"></i>
						</a>
					</li>
				</ul>
			</div>
			
		  <div th:replace="fragments :: navbarFragment"></div>
		</nav>
	  </header>
	  <aside th:replace="fragments :: sidebarFragment"></aside>
  <!-- Left side column. contains the logo and sidebar -->
  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
	  <div class="container-full">
		<!-- Content Header (Page header) -->
		

		<!-- Main content -->
		<section class="content">
		  <div class="row">

			<div class="col-12">

			 <div class="box">
             
				<!-- /.box-header -->
				<div class="box-body">
					<div class="table-responsive">	

                        <div class="col-12">
                            <div class="box">
                                <div class="box-header with-border">
                                    <h3 class="box-title">Mis Reservas </h3> <!-- Título traducido -->
                                </div>
                                <div class="box-body">
                                    <div class="table-responsive">
                                        <table id="example1" class="table table-bordered table-striped">
                                            <thead>
                                                <tr>
                                                    <th>#</th>
                                                    <th>Espacio</th>
                                                    <th>Fecha de Reserva</th>
                                                    <th>Horario</th>
                                                    <th>Medio de Pago</th>
                                                    <th>Fecha de Pago</th>
                                                    <th>Acciones</th>
                                                </tr>
                                            </thead>
											<tbody>
											<tr th:if="${#lists.isEmpty(listaReservas)}">
												<td colspan="7" class="text-center">No ha hecho ninguna reserva.</td>
											</tr>
												<th:block th:each="reserva, idx : ${listaReservas}">
													<tr>
														<td th:text="${idx.index +1}"></td>
														<td th:text="${reserva.espacioDeportivo.getNombre()}"></td>
														<td th:text="${#temporals.format(reserva.fechaReserva, 'dd/MM/yyyy')}"></td>
														<td th:text="${reserva.getHorario().getHoraInicio()} + '-' + ${reserva.getHorario().getHoraFin()}"></td>
														<td th:text="${reserva.getPago().getMedioPago().getNombre()}"></td>
														<td th:text="${#dates.format(reserva.getFechaRegistro(), 'dd/MM/yyyy HH:mm:ss')}"></td>
														<td>
															<button type="button" class="btn btn-primary" th:onclick="'verDetallesReserva(' + ${reserva.getId()} + ')'">

																<i class="mdi mdi-eye"></i>
															</button>
															<button type="button" class="btn btn-danger" data-bs-toggle="modal" th:attr="data-bs-target=${'#modal-cancel-' + reserva.getId()}"
																th:disabled="${reserva.getFechaReserva().isBefore(hoy)}">
																<i class="mdi mdi-cancel"></i>
															</button>
														</td>
													</tr>
												</th:block>
											</tbody>
                                        </table>
                                    </div>
                                </div>   
                            </div>
                        </div>
					</div>
				<!-- /.box-body -->            
				</div>
				<!-- /.box-body -->
			  </div>
			  <!-- /.box -->          
			</div>
			<!-- /.col -->
		  </div>
		  <!-- /.row -->
		</section>
		<!-- /.content -->
	  
	  </div>
  </div>
  <!-- /.content-wrapper -->
		<!-- Modal dinámico para detalles de reserva -->
		<div class="modal center-modal fade" id="modalDetallesReserva" tabindex="-1">
			<div class="modal-dialog modal-lg">
				<div class="modal-content">
					<div class="modal-header">
						<h5 class="modal-title">Detalles de la Reserva</h5>
						<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
					</div>
					<div class="modal-body" id="contenidoDetallesReserva">
						<div class="text-center">
							<div class="spinner-border" role="status">
								<span class="visually-hidden">Cargando...</span>
							</div>
						</div>
					</div>
					<div class="modal-footer modal-footer-uniform">
						<button type="button" class="btn btn-success" data-bs-dismiss="modal">Cerrar</button>
					</div>
				</div>
			</div>
		</div>
		<!-- /.modal -->
	<!-- /.content-wrapper -->
	<div th:each="reserva, idx : ${listaReservas}" class="modal center-modal fade" th:id="'modal-cancel-' + ${reserva.getId()}" tabindex="-1">
		<div class="modal-dialog">
			<div class="modal-content">
				<div class="modal-header">
					<h5 th:utext="'Cancelar Reserva ' + ${idx.index + 1}" class="modal-title"></h5>
					<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
				</div>
				<div class="modal-body">
					<p><strong>¿Está seguro que quiere cancelar su reserva?</strong></p>
					<p><strong>Esta acción es irreversible</strong></p>
				</div>
				<div class="modal-footer modal-footer-uniform">
					<form th:action="@{/vecino/cancelarreserva}" method="post">
						<input type="hidden" th:value="${reserva.getId()}" name="id">
						<button type="submit" class="btn btn-danger">Cancelar Reserva</button>
						<button type="button" class="btn btn-success" data-bs-dismiss="modal">Cerrar</button>
					</form>
				</div>
			</div>
		</div>
	</div>
	<!-- /.modal -->

  <footer class="main-footer">
	&copy; <script>document.write(new Date().getFullYear())</script> <a href="https://munisanmiguel.gob.pe/">Municipalidad de San Miguel</a>. Todos los derechos reservados.
</footer>
  <!-- Control Sidebar -->
  <aside class="control-sidebar">
	  
	<div class="rpanel-title"><span class="pull-right btn btn-circle btn-danger" data-toggle="control-sidebar"><i class="ion ion-close text-white"></i></span> </div>  <!-- Create the tabs -->
    <ul class="nav nav-tabs control-sidebar-tabs">
      <li class="nav-item"><a href="#control-sidebar-home-tab" data-bs-toggle="tab" class="active"><i class="mdi mdi-message-text"></i></a></li>
      <li class="nav-item"><a href="#control-sidebar-settings-tab" data-bs-toggle="tab"><i class="mdi mdi-playlist-check"></i></a></li>
    </ul>
    <!-- Tab panes -->
    <div class="tab-content">
      <!-- Home tab content -->
      <div class="tab-pane active" id="control-sidebar-home-tab">
          <div class="flexbox">
			<a href="javascript:void(0)" class="text-grey">
				<i class="ti-more"></i>
			</a>	
			<p>Users</p>
			<a href="javascript:void(0)" class="text-end text-grey"><i class="ti-plus"></i></a>
		  </div>
		  <div class="lookup lookup-sm lookup-right d-none d-lg-block">
			  <form>
				  <div class="input-group">
					  <input type="search" class="form-control" placeholder="Search" aria-label="Search" aria-describedby="button-addon2">
					  <div class="input-group-append">
						  <button class="btn" type="submit" id="button-addon3"><i data-feather="search"></i></button>
					  </div>
				  </div>
			  </form>
		  </div>
          <div class="media-list media-list-hover mt-20">
			<div class="media py-10 px-0">
			  <a class="avatar avatar-lg status-success" href="#">
				<img src="/images/avatar/1.jpg" alt="...">
			  </a>
			  <div class="media-body">
				<p class="fs-16">
				  <a class="hover-primary" href="#"><strong>Tyler</strong></a>
				</p>
				<p>Praesent tristique diam...</p>
				  <span>Just now</span>
			  </div>
			</div>

			<div class="media py-10 px-0">
			  <a class="avatar avatar-lg status-danger" href="#">
				<img src="/images/avatar/2.jpg" alt="...">
			  </a>
			  <div class="media-body">
				<p class="fs-16">
				  <a class="hover-primary" href="#"><strong>Luke</strong></a>
				</p>
				<p>Cras tempor diam ...</p>
				  <span>33 min ago</span>
			  </div>
			</div>

			<div class="media py-10 px-0">
			  <a class="avatar avatar-lg status-warning" href="#">
				<img src="/images/avatar/3.jpg" alt="...">
			  </a>
			  <div class="media-body">
				<p class="fs-16">
				  <a class="hover-primary" href="#"><strong>Evan</strong></a>
				</p>
				<p>In posuere tortor vel...</p>
				  <span>42 min ago</span>
			  </div>
			</div>

			<div class="media py-10 px-0">
			  <a class="avatar avatar-lg status-primary" href="#">
				<img src="/images/avatar/4.jpg" alt="...">
			  </a>
			  <div class="media-body">
				<p class="fs-16">
				  <a class="hover-primary" href="#"><strong>Evan</strong></a>
				</p>
				<p>In posuere tortor vel...</p>
				  <span>42 min ago</span>
			  </div>
			</div>			
			
			<div class="media py-10 px-0">
			  <a class="avatar avatar-lg status-success" href="#">
				<img src="/images/avatar/1.jpg" alt="...">
			  </a>
			  <div class="media-body">
				<p class="fs-16">
				  <a class="hover-primary" href="#"><strong>Tyler</strong></a>
				</p>
				<p>Praesent tristique diam...</p>
				  <span>Just now</span>
			  </div>
			</div>

			<div class="media py-10 px-0">
			  <a class="avatar avatar-lg status-danger" href="#">
				<img src="/images/avatar/2.jpg" alt="...">
			  </a>
			  <div class="media-body">
				<p class="fs-16">
				  <a class="hover-primary" href="#"><strong>Luke</strong></a>
				</p>
				<p>Cras tempor diam ...</p>
				  <span>33 min ago</span>
			  </div>
			</div>

			<div class="media py-10 px-0">
			  <a class="avatar avatar-lg status-warning" href="#">
				<img src="/images/avatar/3.jpg" alt="...">
			  </a>
			  <div class="media-body">
				<p class="fs-16">
				  <a class="hover-primary" href="#"><strong>Evan</strong></a>
				</p>
				<p>In posuere tortor vel...</p>
				  <span>42 min ago</span>
			  </div>
			</div>

			<div class="media py-10 px-0">
			  <a class="avatar avatar-lg status-primary" href="#">
				<img src="../images/avatar/4.jpg" alt="...">
			  </a>
			  <div class="media-body">
				<p class="fs-16">
				  <a class="hover-primary" href="#"><strong>Evan</strong></a>
				</p>
				<p>In posuere tortor vel...</p>
				  <span>42 min ago</span>
			  </div>
			</div>
			  
		  </div>

      </div>
      <!-- /.tab-pane -->
      <!-- Settings tab content -->
      <div class="tab-pane" id="control-sidebar-settings-tab">
          <div class="flexbox">
			<a href="javascript:void(0)" class="text-grey">
				<i class="ti-more"></i>
			</a>	
			<p>Todo List</p>
			<a href="javascript:void(0)" class="text-end text-grey"><i class="ti-plus"></i></a>
		  </div>
        <ul class="todo-list mt-20">
			<li class="py-15 px-5 by-1">
			  <!-- checkbox -->
			  <input type="checkbox" id="basic_checkbox_1" class="filled-in">
			  <label for="basic_checkbox_1" class="mb-0 h-15"></label>
			  <!-- todo text -->
			  <span class="text-line">Nulla vitae purus</span>
			  <!-- Emphasis label -->
			  <small class="badge bg-danger"><i class="fa fa-clock-o"></i> 2 mins</small>
			  <!-- General tools such as edit or delete-->
			  <div class="tools">
				<i class="fa fa-edit"></i>
				<i class="fa fa-trash-o"></i>
			  </div>
			</li>
			<li class="py-15 px-5">
			  <!-- checkbox -->
			  <input type="checkbox" id="basic_checkbox_2" class="filled-in">
			  <label for="basic_checkbox_2" class="mb-0 h-15"></label>
			  <span class="text-line">Phasellus interdum</span>
			  <small class="badge bg-info"><i class="fa fa-clock-o"></i> 4 hours</small>
			  <div class="tools">
				<i class="fa fa-edit"></i>
				<i class="fa fa-trash-o"></i>
			  </div>
			</li>
			<li class="py-15 px-5 by-1">
			  <!-- checkbox -->
			  <input type="checkbox" id="basic_checkbox_3" class="filled-in">
			  <label for="basic_checkbox_3" class="mb-0 h-15"></label>
			  <span class="text-line">Quisque sodales</span>
			  <small class="badge bg-warning"><i class="fa fa-clock-o"></i> 1 day</small>
			  <div class="tools">
				<i class="fa fa-edit"></i>
				<i class="fa fa-trash-o"></i>
			  </div>
			</li>
			<li class="py-15 px-5">
			  <!-- checkbox -->
			  <input type="checkbox" id="basic_checkbox_4" class="filled-in">
			  <label for="basic_checkbox_4" class="mb-0 h-15"></label>
			  <span class="text-line">Proin nec mi porta</span>
			  <small class="badge bg-success"><i class="fa fa-clock-o"></i> 3 days</small>
			  <div class="tools">
				<i class="fa fa-edit"></i>
				<i class="fa fa-trash-o"></i>
			  </div>
			</li>
			<li class="py-15 px-5 by-1">
			  <!-- checkbox -->
			  <input type="checkbox" id="basic_checkbox_5" class="filled-in">
			  <label for="basic_checkbox_5" class="mb-0 h-15"></label>
			  <span class="text-line">Maecenas scelerisque</span>
			  <small class="badge bg-primary"><i class="fa fa-clock-o"></i> 1 week</small>
			  <div class="tools">
				<i class="fa fa-edit"></i>
				<i class="fa fa-trash-o"></i>
			  </div>
			</li>
			<li class="py-15 px-5">
			  <!-- checkbox -->
			  <input type="checkbox" id="basic_checkbox_6" class="filled-in">
			  <label for="basic_checkbox_6" class="mb-0 h-15"></label>
			  <span class="text-line">Vivamus nec orci</span>
			  <small class="badge bg-info"><i class="fa fa-clock-o"></i> 1 month</small>
			  <div class="tools">
				<i class="fa fa-edit"></i>
				<i class="fa fa-trash-o"></i>
			  </div>
			</li>
			<li class="py-15 px-5 by-1">
			  <!-- checkbox -->
			  <input type="checkbox" id="basic_checkbox_7" class="filled-in">
			  <label for="basic_checkbox_7" class="mb-0 h-15"></label>
			  <!-- todo text -->
			  <span class="text-line">Nulla vitae purus</span>
			  <!-- Emphasis label -->
			  <small class="badge bg-danger"><i class="fa fa-clock-o"></i> 2 mins</small>
			  <!-- General tools such as edit or delete-->
			  <div class="tools">
				<i class="fa fa-edit"></i>
				<i class="fa fa-trash-o"></i>
			  </div>
			</li>
			<li class="py-15 px-5">
			  <!-- checkbox -->
			  <input type="checkbox" id="basic_checkbox_8" class="filled-in">
			  <label for="basic_checkbox_8" class="mb-0 h-15"></label>
			  <span class="text-line">Phasellus interdum</span>
			  <small class="badge bg-info"><i class="fa fa-clock-o"></i> 4 hours</small>
			  <div class="tools">
				<i class="fa fa-edit"></i>
				<i class="fa fa-trash-o"></i>
			  </div>
			</li>
			<li class="py-15 px-5 by-1">
			  <!-- checkbox -->
			  <input type="checkbox" id="basic_checkbox_9" class="filled-in">
			  <label for="basic_checkbox_9" class="mb-0 h-15"></label>
			  <span class="text-line">Quisque sodales</span>
			  <small class="badge bg-warning"><i class="fa fa-clock-o"></i> 1 day</small>
			  <div class="tools">
				<i class="fa fa-edit"></i>
				<i class="fa fa-trash-o"></i>
			  </div>
			</li>
			<li class="py-15 px-5">
			  <!-- checkbox -->
			  <input type="checkbox" id="basic_checkbox_10" class="filled-in">
			  <label for="basic_checkbox_10" class="mb-0 h-15"></label>
			  <span class="text-line">Proin nec mi porta</span>
			  <small class="badge bg-success"><i class="fa fa-clock-o"></i> 3 days</small>
			  <div class="tools">
				<i class="fa fa-edit"></i>
				<i class="fa fa-trash-o"></i>
			  </div>
			</li>
		  </ul>
      </div>
      <!-- /.tab-pane -->
    </div>
  </aside>
  <!-- /.control-sidebar -->
  
  <!-- Add the sidebar's background. This div must be placed immediately after the control sidebar -->
  <div class="control-sidebar-bg"></div>
</div>
<!-- ./wrapper -->
	
	<!-- ./side demo panel -->
	<!-- Sidebar -->
	
	
	<!-- Page Content overlay -->
	
	
	<!-- Vendor JS -->
	<script src="/js/vendors.min.js"></script>
	<script src="/js/pages/chat-popup.js"></script>
    <script src="/assets/icons/feather-icons/feather.min.js"></script>
	<script src="/assets/vendor_components/datatable/datatables.min.js"></script>
	
	<!-- Rhythm Admin App -->
	<script src="/js/template.js"></script>
	
	<script src="/js/pages/data-table.js"></script>

	<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

	<script th:if="${msg != null}" th:inline="javascript">
		Swal.fire({
			icon: 'success',
			title: 'Éxito',
			text: "[[${msg}]]",
			confirmButtonText: 'OK'
		});
	</script>

	<script th:if="${error != null}" th:inline="javascript">
		Swal.fire({
			icon: 'error',
			title: 'Error',
			text: "[[${error}]]",
			confirmButtonText: 'OK'
		});
	</script>

	<script>
		function verDetallesReserva(reservaId) {
			console.log('Abriendo detalles para reserva ID:', reservaId);

			// Obtener elementos del DOM
			const modal = document.getElementById('modalDetallesReserva');
			const contenido = document.getElementById('contenidoDetallesReserva');

			// Mostrar modal con spinner
			const modalInstance = new bootstrap.Modal(modal);
			modalInstance.show();

			contenido.innerHTML = `
				<div class="text-center">
					<div class="spinner-border" role="status">
						<span class="visually-hidden">Cargando...</span>
					</div>
				</div>
			`;

			// Cargar datos de la reserva
			fetch(`/vecino/reservas/detalles/${reservaId}`)
				.then(response => {
					console.log('Response status:', response.status);
					if (!response.ok) {
						throw new Error('Error en la respuesta del servidor');
					}
					return response.json();
				})
				.then(data => {
					console.log('Datos recibidos:', data);
					let contenidoHtml = `
						<div class="row">
							<div class="col-md-6">
								<h6>Información de la Reserva</h6>
								<p><strong>Espacio:</strong> ${data.espacio}</p>
								<p><strong>Fecha de Reserva:</strong> ${new Date(data.fechaReserva).toLocaleDateString()}</p>
								<p><strong>Horario:</strong> ${data.horario}</p>
								<p><strong>Fecha de Registro:</strong> ${new Date(data.fechaRegistro).toLocaleString()}</p>
							</div>
							<div class="col-md-6">
								<h6>Información del Pago</h6>
								<p><strong>Monto:</strong> S/ ${data.montoPagado}</p>
								<p><strong>Estado:</strong> ${getEstadoBadge(data.estadoPago)}</p>
								<p><strong>Medio de Pago:</strong> ${data.medioPago}</p>
								${data.numeroTransaccion ? '<p><strong>Nº Transacción:</strong> ' + data.numeroTransaccion + '</p>' : ''}
							</div>
						</div>
					`;

					// Agregar sección de comprobantes si existen
					if (data.tieneComprobantes && data.comprobantes) {
						contenidoHtml += `
							<hr>
							<h6>Comprobantes de Pago</h6>
							<div class="row">
						`;
						data.comprobantes.forEach((comprobante, index) => {
							contenidoHtml += `
								<div class="col-md-6 mb-3">
									<div class="card">
										<div class="card-body text-center">
											<p class="card-title">📄 Comprobante ${index + 1}</p>
											<a href="${comprobante.url}" target="_blank" class="btn btn-primary btn-sm">
												<i class="fas fa-download"></i> Descargar
											</a>
											<br>
											<small class="text-muted">${comprobante.nombre || 'Archivo'}</small>
										</div>
									</div>
								</div>
							`;
						});
						contenidoHtml += `</div>`;
					}

					contenido.innerHTML = contenidoHtml;
				})
				.catch(error => {
					console.error('Error:', error);
					contenido.innerHTML = `
						<div class="alert alert-danger">
							<strong>Error:</strong> No se pudieron cargar los detalles de la reserva.
						</div>
					`;
				});
		}

		function getEstadoBadge(estado) {
			switch(estado) {
				case 'APROBADO':
					return '<span class="badge bg-success">Aprobado</span>';
				case 'PENDIENTE':
					return '<span class="badge bg-warning">Pendiente</span>';
				case 'RECHAZADO':
					return '<span class="badge bg-danger">Rechazado</span>';
				default:
					return '<span class="badge bg-secondary">' + estado + '</span>';
			}
		}
	</script>





</body>
</html>