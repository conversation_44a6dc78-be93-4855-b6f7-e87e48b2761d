<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<meta name="description" content="">
	<meta name="author" content="">
	<link rel="icon" href="/images/favicon.ico">

	<title>Superadmin - Baneos</title>

	<!-- Vendors Style-->
	<link rel="stylesheet" href="/css/vendors_css.css">

	<!-- Style-->
	<link rel="stylesheet" href="/css/style.css">
	<link rel="stylesheet" href="/css/skin_color.css">

</head>

<body class="hold-transition light-skin sidebar-mini theme-success fixed">

<div class="wrapper">
	<div id="loader"></div>

	<header class="main-header">
		<div class="d-flex align-items-center logo-box justify-content-center">
			<!-- Logo -->
			<a href="principal.html" class="logo">
				<!-- logo-->
				<div class="logo-mini w-150 text-center">
					<span class="light-logo"><img src="/images/logo-sanMiguel.png" alt="logo"></span>
				</div>
			</a>
		</div>

		<!-- Header Navbar -->
		<nav class="navbar navbar-static-top">
			<!-- Sidebar toggle button-->
			<div class="app-menu">
				<ul class="header-megamenu nav">
					<li class="btn-group nav-item">
						<a href="#" class="waves-effect waves-light nav-link push-btn btn-primary-light" data-toggle="push-menu" role="button">
							<i data-feather="align-left"></i>
						</a>
					</li>

				</ul>
			</div>

			<div class="navbar-custom-menu r-side">
				<ul class="nav navbar-nav">
					<li class="btn-group nav-item d-lg-inline-flex d-none">
						<a href="#" data-provide="fullscreen" class="waves-effect waves-light nav-link full-screen btn-warning-light" title="Full Screen">
							<i data-feather="maximize"></i>
						</a>
					</li>
					<!-- User Account-->
					<li class="dropdown user user-menu">
						<a href="#" class="waves-effect waves-light dropdown-toggle w-auto l-h-12 bg-transparent py-0 no-shadow" data-bs-toggle="dropdown" title="User">
							<div class="d-flex pt-5">

							</div>
						</a>
						<ul class="dropdown-menu animated flipInX">
							<li class="user-body">
								<a class="dropdown-item" href="#"><i class="ti-user text-muted me-2"></i> Profile</a>
								<a class="dropdown-item" href="#"><i class="ti-wallet text-muted me-2"></i> My Wallet</a>
								<a class="dropdown-item" href="#"><i class="ti-settings text-muted me-2"></i> Settings</a>
								<div class="dropdown-divider"></div>
								<a class="dropdown-item" href="#"><i class="ti-lock text-muted me-2"></i> Logout</a>
							</li>
						</ul>
					</li>

				</ul>
			</div>
		</nav>
	</header>

	<aside class="main-sidebar">
		<section class="sidebar position-relative">

			<div class="multinav">
				<div class="multinav-scroll" style="height: 100%;">
					<!-- sidebar menu-->
					<ul class="sidebar-menu" data-widget="tree">
						<li>
							<a href="/SuperAdmin">
								<i data-feather="monitor"></i>
								<span>Dashboard</span>

							</a>
						</li>
						<li>
							<a href="/SuperAdmin/usuarios-baneados">
								<i data-feather="calendar"></i>
								<span>Baneos</span>
							</a>
						</li>
						<li>
							<a href="/SuperAdmin/usuarios-no-baneados">
								<i data-feather="users"></i>
								<span>Usuarios</span>
							</a>
						</li>
						<li>
							<a href="#" class="nav-link" onclick="document.getElementById('logoutForm2').submit(); return false;">
								<i data-feather="log-out"></i>
								<span>Cerrar sesión</span>
							</a>
							<form id="logoutForm2" th:action="@{/logout}" method="post" style="display: none;"></form>
						</li>
					</ul>
				</div>
			</div>
		</section>
	</aside>

	<div class="content-wrapper">
		<div class="container-full">


			<section class="content">
				<div class="row">
					<div class="col-12">
						<div class="box">
							<div class="box-body">
								<div class="table-responsive">
									<div class="col-12">
										<div class="box">
											<div class="box-header with-border">
												<h3 class="box-title">Tabla de los Usuarios Baneados</h3> <!-- Título traducido -->
											</div>
											<div class="box-body">
												<div class="table-responsive">
													<table id="example1" class="table table-bordered table-striped">
														<thead>
														<tr>
															<th># ID</th>
															<th>Nombres</th>
															<th>Apellidos</th>
															<th>Cargo</th>
															<th>Número Telefónico</th>
															<th>Dirección</th>
															<th>Acción</th>
														</tr>
														</thead>
														<tbody>
														<tr th:each="usuario : ${baneados}">
															<td th:text="${usuario.id}"></td> <!-- ID -->
															<td th:text="${usuario.nombres}"></td>
															<td th:text="${usuario.apellidos}"></td>
															<td th:text="${usuario.rol.nombre}"></td>
															<td th:text="${usuario.numCelular}"></td>
															<td th:text="${usuario.direccion}"></td>
															<td>
																<!-- Botón único para abrir modal -->
																<button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" th:attr="data-bs-target=${'#modal-' + usuario.id}" title="Desbanear">
																	<i class="fa fa-unlock"></i>
																</button>
															</td>
														</tr>
														</tbody>

														<!-- Modales para cada usuario baneado -->
														<div th:each="usuario : ${baneados}" class="modal center-modal fade" th:id="'modal-' + ${usuario.id}" tabindex="-1" aria-labelledby="modalLabel" aria-hidden="true">
															<div class="modal-dialog">
																<div class="modal-content">
																	<div class="modal-header bg-primary text-white">
																		<h5 th:utext="'Desbanear Usuario  '" class="modal-title"></h5>
																		<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Cerrar"></button>
																	</div>
																	<div class="modal-body">
																		<p>¿Está seguro que desea desbanear al usuario <strong th:text="${usuario.nombres} + ' ' + ${usuario.apellidos}"></strong>?</p>
																	</div>

																	<div class="modal-footer modal-footer-uniform">
																		<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
																		<a th:href="@{/SuperAdmin/usuarios/desbanear/{id}(id=${usuario.id})}" class="btn btn-primary">Confirmar Desbaneo</a>
																	</div>
																</div>
															</div>
														</div>

													</table>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</section>
		</div>
	</div>

	<footer class="main-footer">
		&copy; <script>document.write(new Date().getFullYear())</script> <a href="https://www.multipurposethemes.com/">Multipurpose Themes</a>. All Rights Reserved.
	</footer>

	<aside class="control-sidebar">
		<div class="rpanel-title"><span class="pull-right btn btn-circle btn-danger" data-toggle="control-sidebar"><i class="ion ion-close text-white"></i></span> </div>
		<ul class="nav nav-tabs control-sidebar-tabs">
			<li class="nav-item"><a href="#control-sidebar-home-tab" data-bs-toggle="tab" class="active"><i class="mdi mdi-message-text"></i></a></li>
			<li class="nav-item"><a href="#control-sidebar-settings-tab" data-bs-toggle="tab"><i class="mdi mdi-playlist-check"></i></a></li>
		</ul>
	</aside>

	<div class="control-sidebar-bg"></div>
</div>

<script src="/js/vendors.min.js"></script>
<script src="/js/pages/chat-popup.js"></script>
<script src="/assets/icons/feather-icons/feather.min.js"></script>
<script src="/assets/vendor_components/datatable/datatables.min.js"></script>
<script src="/js/template.js"></script>
<script>
	$('#example1').DataTable({
		language: {
			decimal: ",",
			emptyTable: "No hay datos disponibles en la tabla",
			infoFiltered: "(filtrado de _MAX_ registros totales)",
			infoPostFix: "",
			thousands: ".",
			lengthMenu: "Mostrar _MENU_ registros",
			loadingRecords: "Cargando...",
			processing: "Procesando...",
			search: "Buscar:",
			zeroRecords: "No se encontraron registros coincidentes",
			paginate: {
				first: "Primero",
				last: "Último",
				next: "Siguiente",
				previous: "Anterior"
			},
			aria: {
				sortAscending: ": activar para ordenar la columna ascendente",
				sortDescending: ": activar para ordenar la columna descendente"
			}
		},
		lengthChange: false,  // Oculta "Mostrar __ registros"
		info: false,          // Oculta "Mostrando 1 a X de Y registros"
		columnDefs: [
			{
				targets: 0, // Primera columna (#ID)
				orderable: false,
				searchable: false,
				render: function (data, type, row, meta) {
					return meta.row + 1 + meta.settings._iDisplayStart;
				}
			}
		]
	});
</script>


</script>


</body>
</html>
