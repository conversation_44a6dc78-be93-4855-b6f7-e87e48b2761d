<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta name="description" content="">
  <meta name="author" content="">
  <link rel="icon" th:href="@{/images/logo-solo.png}">

  <title>Página principal - Coordinador</title>

  <!-- Vendors Style-->
  <link rel="stylesheet" th:href="@{/css/vendors_css.css}" >

  <!-- Font Awesome -->
  <link rel="stylesheet" th:href="@{/assets/icons/font-awesome/css/font-awesome.min.css}">

  <!-- Style-->
  <link rel="stylesheet" th:href="@{/css/style.css}">
  <link rel="stylesheet" th:href="@{/css/skin_color.css}">
  <style>

    #recuadro {
      background-color: rgba(183, 225, 233, 0.185);
      border-radius: 12px;
      padding: 25px 35px;
      box-shadow: 0 4px 8px rgba(0,0,0,0.1);
      text-align: center;
    }

    #fecha {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      font-size: 15px;
      color: #7caad4;
      margin-bottom: -10px;
    }

    #hora {
      font-size: 45px;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      font-weight:bold;
      color: #7caad4;
      margin-bottom: 5px;
    }
  </style>

</head>

<body class="hold-transition light-skin sidebar-mini theme-success fixed">

<div class="wrapper">
  <div id="loader"></div>

  <header class="main-header">
    <div class="d-flex align-items-center logo-box justify-content-center">
      <!-- Logo -->
      <a th:href="@{/coordinador/principal}" class="logo">
        <!-- logo-->
        <div class="logo-mini w-150 text-center">
          <span class="light-logo"><img th:src="@{/images/logo-sanMiguel.png}" alt="logo"></span>
        </div>
      </a>
    </div>
    <!-- Header Navbar -->
    <nav class="navbar navbar-static-top">
      <!-- Sidebar toggle button-->
      <div class="app-menu">
        <ul class="header-megamenu nav">
          <li class="btn-group nav-item">
            <a href="#" class="waves-effect waves-light nav-link push-btn btn-primary-light" data-toggle="push-menu" role="button">
              <i data-feather="align-left"></i>
            </a>
          </li>
        </ul>
      </div>

      <div class="navbar-custom-menu r-side">
        <ul class="nav navbar-nav">
          <!-- Notifications -->
          <li class="dropdown notifications-menu">
            <a href="#" class="waves-effect waves-light dropdown-toggle btn-info-light" data-bs-toggle="dropdown" title="Notifications">
              <i data-feather="bell"></i>
            </a>
            <ul class="dropdown-menu animated bounceIn">

              <li class="header">
                <div class="p-20">
                  <div class="flexbox">
                    <div>
                      <h4 class="mb-0 mt-0">Notifications</h4>
                    </div>
                    <div>
                      <a href="#" class="text-danger">Clear All</a>
                    </div>
                  </div>
                </div>
              </li>

              <li>
                <!-- inner menu: contains the actual data -->
                <ul class="menu sm-scrol">
                  <li>
                    <a href="#">
                      <i class="fa fa-users text-info"></i> Curabitur id eros quis nunc suscipit blandit.
                    </a>
                  </li>
                  <li>
                    <a href="#">
                      <i class="fa fa-warning text-warning"></i> Duis malesuada justo eu sapien elementum, in semper diam posuere.
                    </a>
                  </li>
                  <li>
                    <a href="#">
                      <i class="fa fa-users text-danger"></i> Donec at nisi sit amet tortor commodo porttitor pretium a erat.
                    </a>
                  </li>
                  <li>
                    <a href="#">
                      <i class="fa fa-shopping-cart text-success"></i> In gravida mauris et nisi
                    </a>
                  </li>
                  <li>
                    <a href="#">
                      <i class="fa fa-user text-danger"></i> Praesent eu lacus in libero dictum fermentum.
                    </a>
                  </li>
                  <li>
                    <a href="#">
                      <i class="fa fa-user text-primary"></i> Nunc fringilla lorem
                    </a>
                  </li>
                  <li>
                    <a href="#">
                      <i class="fa fa-user text-success"></i> Nullam euismod dolor ut quam interdum, at scelerisque ipsum imperdiet.
                    </a>
                  </li>
                </ul>
              </li>
              <li class="footer">
                <a href="#">View all</a>
              </li>
            </ul>
          </li>


          <!-- User Account-->
          <li>
            <a class="waves-effect waves-light dropdown-toggle w-auto l-h-12 bg-transparent py-0 no-shadow" data-bs-toggle="dropdown" title="User">
              <div class="d-flex pt-5">
                <div class="text-end me-10">
                  <p class="pt-5 fs-14 mb-0 fw-700 text-primary" th:text="${session.usuario.nombres+ ' ' + session.usuario.apellidos}" ></p>
                  <small class="fs-10 mb-0 text-uppercase text-mute" th:text="${session.usuario.rol.nombre}"></small>
                </div>
                <img th:src="@{|/coordinador/profileimage/${session.usuario.getId()}?t=${#dates.format(#dates.createNow(), 'yyyyMMddHHmmss')}|}" class="avatar rounded-10 bg-primary-light h-40 w-40" alt=""
                     onerror="this.onerror=null;this.src='/images/avatar/avatar-1.png';" />
              </div>
            </a>
          </li>

        </ul>
      </div>
    </nav>
  </header>

  <!-- Left side column. contains the logo and sidebar -->
  <aside class="main-sidebar">
    <!-- sidebar-->
    <section class="sidebar position-relative">
      <div class="multinav">
        <div class="multinav-scroll" style="height: 100%;">
          <!-- sidebar menu-->
          <ul class="sidebar-menu" data-widget="tree">
            <li>
              <a th:href="@{/coordinador/perfil(id=3)}">
                <i data-feather="user"></i>
                <span>Perfil</span>
              </a>
            </li>
            <li>
              <a th:href="@{/coordinador/principal}">
                <i data-feather="clock"></i>
                <span>Marcar asistencia</span>
              </a>
            </li>
            <li>
              <a th:href="@{/coordinador/mis-observaciones}">
                <i data-feather="file-text"></i>
                <span>Mis Observaciones</span>
              </a>
            </li>
            <!--Implementación proxima de horarios
            <li>
              <a href="horarios.html">
                <i data-feather="calendar"></i>
                <span>Horarios</span>
              </a>
            </li>-->

            <li>
              <a href="#" class="nav-link" onclick="document.getElementById('logoutForm').submit(); return false;">
                <i data-feather="log-out"></i>
                <span>Cerrar sesión</span>
              </a>
              <form id="logoutForm" th:action="@{/logout}" method="post" style="display: none;"></form>
            </li>

          </ul>

          <div class="sidebar-widgets">
            <div class="copyright text-center m-25">
              <p><strong class="d-block">Municipalidad de San Miguel</strong> © <script>document.write(new Date().getFullYear())</script> All Rights Reserved</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  </aside>

  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <div class="container-full">
      <!-- Content Header (Page header) -->
      <div class="content-header">
        <div class="d-flex align-items-center">
          <div class="me-auto">
            <h4 class="page-title">Perfil</h4>
            <div class="d-inline-block align-items-center">
              <nav>
                <ol class="breadcrumb">
                  <li class="breadcrumb-item"><a href="#"><i class="mdi mdi-home-outline"></i></a></li>
                  <li class="breadcrumb-item" aria-current="page">Menú</li>
                </ol>
              </nav>
            </div>
          </div>

        </div>
      </div>

      <!-- Main content -->
      <section class="content">
        <div class="row">
          <div class="col-lg-5 col-12">
            <div class="box" style="background-color: rgba(136, 217, 231, 0.5);">
              <!-- /.box-header -->

              <div id="recuadro">
                <div id="fecha">Cargando fecha...</div>
                <div id="hora">00:00:00</div>
                <div id="frase">Marque su hora de entrada al complejo</div>
                <div style="text-align: center;padding: 10px;">
                  <button type="button" class="btn btn-warning me-1" id="btnEntrada" disabled>
                    Entrada
                  </button>
                  <button type="button" class="btn btn-primary" id="btnSalida" disabled>
                    Salida
                  </button>
                </div>
              </div>



            </div>
            <!-- /.box -->
          </div>

          <div class="col-lg-7 col-12">
            <div class="box">
              <div class="box-header with-border">
                <h4 class="box-title">Observaciones</h4>
              </div>
              <!-- /.box-header -->
              <form class="form" th:action="@{/coordinador/guardar-observacion}" method="post" enctype="multipart/form-data">
                <div class="box-body">
                  <!-- Mostrar mensajes de éxito o error -->
                  <div th:if="${success}" class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fa fa-check-circle me-2"></i>
                    <span th:text="${success}"></span>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                  </div>

                  <div th:if="${error}" class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fa fa-exclamation-triangle me-2"></i>
                    <span th:text="${error}"></span>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                  </div>

                  <h4 class="box-title text-info mb-0"><i class="ti-map-alt me-15"></i>Establecimiento</h4>
                  <hr class="my-15">
                  <div class="row">
                    <div class="col-md-12">
                      <div class="form-group">
                        <label class="form-label">Espacio Deportivo<span class="text-danger">*</span></label>
                        <select id="espacioId" name="espacioId" required class="form-select">
                          <option value="" disabled selected>Seleccione un espacio deportivo</option>
                          <option th:each="espacio : ${espaciosDeportivos}"
                                  th:value="${espacio.id}"
                                  th:text="${espacio.nombre + ' - ' + espacio.ubicacion + ' (' + espacio.tipoEspacio.nombre + ')'}">
                          </option>
                        </select>
                      </div>
                    </div>
                  </div>
                  <h4 class="box-title text-info mb-0 mt-20"><i class="ti-comment-alt me-10"></i> Comentarios</h4>
                  <hr class="my-15">
                  <div class="row">
                    <div class="radio; col-md-6">
                      <input name="tipoComentario" type="radio" id="Option_1" value="reparacion" checked>
                      <label for="Option_1">🔧 Reparación/Mantenimiento</label>
                    </div>
                    <div class="radio; col-md-6">
                      <input name="tipoComentario" type="radio" id="Option_2" value="observacion">
                      <label for="Option_2">📝 Observación General</label>
                    </div>
                  </div>

                  <!-- Campo de prioridad (solo para reparaciones) -->
                  <div class="form-group" id="prioridadGroup" style="margin-top: 15px;">
                    <label class="form-label">Prioridad<span class="text-danger">*</span></label>
                    <select name="prioridad" id="prioridad" class="form-select">
                      <option value="BAJA">🟢 Baja - No urgente</option>
                      <option value="MEDIA" selected>🟡 Media - Atención normal</option>
                      <option value="ALTA">🟠 Alta - Requiere pronta atención</option>
                      <option value="CRITICA">🔴 Crítica - Atención inmediata</option>
                    </select>
                  </div>

                  <div class="form-group" style="margin-top: 15px;">
                    <label class="form-label">Descripción<span class="text-danger">*</span></label>
                    <textarea rows="4" class="form-control" name="comentarios" id="comentarios" required
                              placeholder="Explique brevemente la situación por la cual el establecimiento requiere arreglos o alguna observación sobre este."></textarea>
                  </div>

                  <!-- Campo para subida de imágenes -->
                  <div class="form-group" style="margin-top: 15px;">
                    <label class="form-label">Imágenes de apoyo <small class="text-muted">(Opcional, máximo 4 imágenes)</small></label>
                    <input type="file" class="form-control" name="imagenes" id="imagenes"
                           accept="image/*" multiple>
                    <small class="form-text text-muted">
                      <i class="fa fa-info-circle"></i> Seleccione hasta 4 imágenes para documentar la observación
                    </small>
                    <div id="preview-imagenes" class="mt-2"></div>
                  </div>
                </div>
                <!-- /.box-body -->
                <div class="box-footer text-center">
                  <button type="submit" class="btn btn-primary">
                    <i class="fa fa-paper-plane me-2"></i>Enviar Observación
                  </button>
                  <a href="/coordinador/mis-observaciones" class="btn btn-outline-info ms-2">
                    <i class="fa fa-list me-2"></i>Ver Mis Observaciones
                  </a>
                </div>
              </form>
            </div>
            <!-- /.box -->
          </div>

        </div>


      </section>
      <!-- /.content -->
    </div>
  </div>
  <!-- /.content-wrapper -->

  <footer class="main-footer">
    &copy; <script>document.write(new Date().getFullYear())</script> <a>New Fibra</a>. All Rights Reserved.
  </footer>
  <!-- Control Sidebar -->
  <aside class="control-sidebar">

    <div class="rpanel-title"><span class="pull-right btn btn-circle btn-danger" data-toggle="control-sidebar"><i class="ion ion-close text-white"></i></span> </div>  <!-- Create the tabs -->
    <ul class="nav nav-tabs control-sidebar-tabs">
      <li class="nav-item"><a href="#control-sidebar-home-tab" data-bs-toggle="tab" class="active"><i class="mdi mdi-message-text"></i></a></li>
      <li class="nav-item"><a href="#control-sidebar-settings-tab" data-bs-toggle="tab"><i class="mdi mdi-playlist-check"></i></a></li>
    </ul>
  </aside>
  <!-- /.control-sidebar -->
  <!-- Add the sidebar's background. This div must be placed immediately after the control sidebar -->
  <div class="control-sidebar-bg"></div>
</div>

<!--Script del sistema de asistencia con geolocalización!-->
<!-- ./wrapper -->
<script>
  // Variables globales
  let inicioTrabajo = null;
  let intervaloContador = null;
  let intervaloReloj = null;
  let tiempoTranscurrido = 0;
  let espaciosPermitidos = [];
  let estadoAsistencia = {
    tieneEntrada: false,
    tieneSalida: false
  };

  // Función para obtener espacios deportivos con coordenadas
  async function cargarEspaciosPermitidos() {
    try {
      const response = await fetch('/coordinador/espacios-coordenadas');
      const data = await response.json();

      if (data.success) {
        espaciosPermitidos = data.espacios;
        console.log('Espacios cargados:', espaciosPermitidos.length);

        if (espaciosPermitidos.length === 0) {
          console.warn('No hay espacios con coordenadas configuradas');
          document.getElementById('frase').textContent = 'No tienes horarios asignados para hoy. Contacta al administrador.';
          document.getElementById('frase').style.color = '#ffc107';
          // Deshabilitar botones
          document.getElementById('btnEntrada').disabled = true;
          document.getElementById('btnSalida').disabled = true;
        } else {
          console.log('Espacios disponibles:', espaciosPermitidos.map(e => e.nombre));
          // Verificar estado de asistencia al cargar espacios
          await cargarEstadoAsistencia();
        }
      } else {
        console.error('Error al cargar espacios:', data.error);
        document.getElementById('frase').textContent = 'Error al cargar la configuración de ubicaciones';
        document.getElementById('frase').style.color = '#dc3545';
      }
    } catch (error) {
      console.error('Error al cargar espacios:', error);
      document.getElementById('frase').textContent = 'Error de conexión al cargar ubicaciones';
      document.getElementById('frase').style.color = '#dc3545';
    }
  }

  // Función mejorada de geolocalización
  async function obtenerUbicacion() {
    return new Promise((resolve, reject) => {
      if (!navigator.geolocation) {
        reject("Tu navegador no soporta geolocalización");
        return;
      }

      const opciones = {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 0
      };

      navigator.geolocation.getCurrentPosition(
        position => resolve(position.coords),
        error => {
          switch(error.code) {
            case error.PERMISSION_DENIED:
              reject("Debes permitir el acceso a tu ubicación para usar esta función");
              break;
            case error.POSITION_UNAVAILABLE:
              reject("No se pudo obtener tu ubicación. Verifica que el GPS esté activado.");
              break;
            case error.TIMEOUT:
              reject("Tiempo de espera agotado al obtener ubicación");
              break;
            default:
              reject("Error desconocido al obtener ubicación");
          }
        },
        opciones
      );
    });
  }

  // Función de validación de ubicación
  async function validarUbicacion() {
    try {
      if (espaciosPermitidos.length === 0) {
        throw new Error("No tienes horarios asignados para hoy o no hay espacios con coordenadas configuradas");
      }

      const coords = await obtenerUbicacion();
      console.log('Coordenadas obtenidas:', coords.latitude, coords.longitude);

      let dentroDeArea = false;
      let espacioMasCercano = null;
      let distanciaMinima = Infinity;

      for (const espacio of espaciosPermitidos) {
        if (espacio.latitud && espacio.longitud) {
          const distancia = calcularDistancia(
            coords.latitude,
            coords.longitude,
            parseFloat(espacio.latitud),
            parseFloat(espacio.longitud)
          );

          const radioPermitido = espacio.radioCobertura || 1700; // 100m por defecto

          console.log(`Distancia a ${espacio.nombre}: ${Math.round(distancia)}m (radio: ${radioPermitido}m)`);

          if (distancia <= radioPermitido) {
            dentroDeArea = true;
            break;
          }

          if (distancia < distanciaMinima) {
            distanciaMinima = distancia;
            espacioMasCercano = espacio;
          }
        }
      }

      if (!dentroDeArea) {
        const mensaje = espacioMasCercano
          ? `Fuera del área permitida. Más cercano: ${espacioMasCercano.nombre} (${Math.round(distanciaMinima)}m)`
          : "Fuera del área permitida de cualquier espacio deportivo";
        throw new Error(mensaje);
      }

      // Habilitar botones según el estado actual
      actualizarEstadoBotones();
      return { coords, valido: true };

    } catch (error) {
      console.error('Error de ubicación:', error);
      document.getElementById('btnEntrada').disabled = true;
      document.getElementById('btnSalida').disabled = true;

      // Mostrar error en la interfaz
      document.getElementById('frase').textContent = error.message;
      document.getElementById('frase').style.color = '#dc3545';

      return { valido: false, error: error.message };
    }
  }

  // Función para cargar estado actual de asistencia
  async function cargarEstadoAsistencia() {
    try {
      const response = await fetch('/coordinador/estado-asistencia');
      const data = await response.json();

      if (data.success) {
        estadoAsistencia = data;
        actualizarEstadoBotones();

        if (data.tieneEntrada && !data.tieneSalida) {
          // Ya marcó entrada, iniciar cronómetro
          iniciarCronometro();
        }
      }
    } catch (error) {
      console.error('Error al cargar estado:', error);
    }
  }

  // Función para actualizar estado de botones
  function actualizarEstadoBotones() {
    const btnEntrada = document.getElementById('btnEntrada');
    const btnSalida = document.getElementById('btnSalida');
    const frase = document.getElementById('frase');

    if (estadoAsistencia.tieneEntrada && estadoAsistencia.tieneSalida) {
      // Ya completó su jornada
      btnEntrada.disabled = true;
      btnSalida.disabled = true;
      frase.textContent = "Jornada completada";
      frase.style.color = '#28a745';
      System.out.println("Ingreso exitoso");
    } else if (estadoAsistencia.tieneEntrada) {
      // Ya marcó entrada, puede marcar salida
      btnEntrada.disabled = true;
      btnSalida.disabled = false;
      frase.textContent = "Tiempo trabajado:";
      frase.style.color = '#007bff';
    } else {
      // Puede marcar entrada
      btnEntrada.disabled = false;
      btnSalida.disabled = true;
      frase.textContent = "Marque su hora de entrada al complejo";
      frase.style.color = '#6c757d';
    }
  }

  // Función para iniciar cronómetro
  function iniciarCronometro() {
    if (estadoAsistencia.horaEntrada) {
      const horaEntrada = new Date();
      const [horas, minutos, segundos] = estadoAsistencia.horaEntrada.split(':');
      horaEntrada.setHours(parseInt(horas), parseInt(minutos), parseInt(segundos));

      const ahora = new Date();
      tiempoTranscurrido = Math.floor((ahora - horaEntrada) / 1000);

      inicioTrabajo = horaEntrada;

      // Iniciar cronómetro
      intervaloContador = setInterval(() => {
        tiempoTranscurrido++;
        document.getElementById('hora').textContent = formatearTiempo(tiempoTranscurrido);
      }, 1000);
    }
  }

  // Función para registrar entrada
  async function registrarEntrada() {
    try {
      const validacion = await validarUbicacion();
      if (!validacion.valido) return;

      const coords = validacion.coords;

      const formData = new FormData();
      formData.append('latitud', coords.latitude);
      formData.append('longitud', coords.longitude);

      const response = await fetch('/coordinador/registrar-entrada', {
        method: 'POST',
        body: formData
      });

      const data = await response.json();

      if (data.success) {
        estadoAsistencia.tieneEntrada = true;
        estadoAsistencia.horaEntrada = data.horaEntrada;

        iniciarCronometro();
        actualizarEstadoBotones();

        let mensaje = 'Entrada registrada exitosamente';
        if (data.estado === 'TARDE' && data.minutosRetraso) {
          mensaje += ` (${data.minutosRetraso} minutos de retraso)`;
        }

        alert(mensaje);
      } else {
        alert('Error: ' + data.error);
      }
    } catch (error) {
      alert('Error de conexión: ' + error.message);
    }
  }

  // Función para registrar salida
  async function registrarSalida() {
    try {
      const validacion = await validarUbicacion();
      if (!validacion.valido) return;

      const coords = validacion.coords;

      const formData = new FormData();
      formData.append('latitud', coords.latitude);
      formData.append('longitud', coords.longitude);

      const response = await fetch('/coordinador/registrar-salida', {
        method: 'POST',
        body: formData
      });

      const data = await response.json();

      if (data.success) {
        estadoAsistencia.tieneSalida = true;
        estadoAsistencia.horaSalida = data.horaSalida;
        estadoAsistencia.horasTrabajadas = data.horasTrabajadas;

        // Detener cronómetro
        clearInterval(intervaloContador);
        inicioTrabajo = null;

        actualizarEstadoBotones();

        let mensaje = `Salida registrada exitosamente. Horas trabajadas: ${data.horasTrabajadas}`;
        if (data.estado === 'TARDE') {
          mensaje += ' (No cumplió con las horas mínimas)';
        }

        alert(mensaje);

        // Restaurar reloj normal
        actualizarReloj();
      } else {
        alert('Error: ' + data.error);
      }
    } catch (error) {
      alert('Error de conexión: ' + error.message);
    }
  }

  // Funciones auxiliares
  function calcularDistancia(lat1, lon1, lat2, lon2) {
    const R = 6371e3; // Radio de la Tierra en metros
    const φ1 = lat1 * Math.PI / 180;
    const φ2 = lat2 * Math.PI / 180;
    const Δφ = (lat2 - lat1) * Math.PI / 180;
    const Δλ = (lon2 - lon1) * Math.PI / 180;

    const a = Math.sin(Δφ/2) ** 2 + Math.cos(φ1) * Math.cos(φ2) * Math.sin(Δλ/2) ** 2;
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  }

  function formatearTiempo(segundos) {
    const horas = Math.floor(segundos / 3600);
    const minutos = Math.floor((segundos % 3600) / 60);
    const segs = segundos % 60;
    return `${String(horas).padStart(2, '0')}:${String(minutos).padStart(2, '0')}:${String(segs).padStart(2, '0')}`;
  }

  function actualizarReloj() {
    const fecha = new Date();
    const opcionesFecha = {
      weekday: 'long',
      day: 'numeric',
      month: 'long'
    };
    const opcionesHora = {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    };

    // Actualizar fecha
    document.getElementById('fecha').textContent = fecha.toLocaleDateString('es-PE', opcionesFecha);

    // Actualizar hora solo si no está en modo cronómetro
    if (!inicioTrabajo) {
      document.getElementById('hora').textContent = fecha.toLocaleTimeString('es-PE', opcionesHora);
    }
  }

  // Inicialización principal
  document.addEventListener('DOMContentLoaded', async function() {
    // Configurar eventos de botones
    document.getElementById('btnEntrada').addEventListener('click', registrarEntrada);
    document.getElementById('btnSalida').addEventListener('click', registrarSalida);

    // Iniciar reloj
    intervaloReloj = setInterval(actualizarReloj, 1000);
    actualizarReloj();

    // Cargar configuración inicial
    await cargarEspaciosPermitidos();
    // cargarEstadoAsistencia se llama desde cargarEspaciosPermitidos si hay espacios

    // Validación inicial de ubicación
    setTimeout(() => {
      validarUbicacion().then(resultado => {
        if (resultado.valido) {
          console.log('Ubicación válida, sistema listo');
          // Programar validación periódica cada 2 minutos
          setInterval(validarUbicacion, 120000);
        }
      });
    }, 1000);

    // Configurar formulario de observaciones
    configurarFormularioObservaciones();
  });

  // Configuración del formulario de observaciones
  function configurarFormularioObservaciones() {
    const espacioSelect = document.getElementById('espacioId');
    const comentariosTextarea = document.getElementById('comentarios');
    const radioButtons = document.querySelectorAll('input[name="tipoComentario"]');
    const prioridadGroup = document.getElementById('prioridadGroup');

    // Configurar previsualización de imágenes
    configurarPreviewImagenes();

    // Mostrar/ocultar campo de prioridad según tipo de comentario
    function togglePrioridad() {
      const tipoSeleccionado = document.querySelector('input[name="tipoComentario"]:checked').value;
      if (tipoSeleccionado === 'reparacion') {
        prioridadGroup.style.display = 'block';
        document.getElementById('prioridad').required = true;
      } else {
        prioridadGroup.style.display = 'none';
        document.getElementById('prioridad').required = false;
      }
      actualizarPlaceholder();
    }

    // Actualizar placeholder según el tipo de comentario seleccionado
    function actualizarPlaceholder() {
      const tipoSeleccionado = document.querySelector('input[name="tipoComentario"]:checked').value;
      if (tipoSeleccionado === 'reparacion') {
        comentariosTextarea.placeholder = '🔧 Describa detalladamente el problema o reparación necesaria. Incluya ubicación específica, urgencia y cualquier detalle relevante para el mantenimiento.';
      } else {
        comentariosTextarea.placeholder = '📝 Comparta su observación general sobre el espacio deportivo. Puede incluir sugerencias de mejora, comentarios sobre el estado actual o cualquier aspecto relevante.';
      }
    }

    // Agregar event listeners a los radio buttons
    radioButtons.forEach(radio => {
      radio.addEventListener('change', togglePrioridad);
    });

    // Validación del formulario
    const form = document.querySelector('form');
    form.addEventListener('submit', function(e) {
      const espacioId = espacioSelect.value;
      const comentarios = comentariosTextarea.value.trim();
      const tipoComentario = document.querySelector('input[name="tipoComentario"]:checked').value;

      if (!espacioId) {
        e.preventDefault();
        alert('⚠️ Por favor, seleccione un espacio deportivo.');
        espacioSelect.focus();
        return;
      }

      if (!comentarios) {
        e.preventDefault();
        alert('⚠️ Por favor, ingrese una descripción.');
        comentariosTextarea.focus();
        return;
      }

      if (comentarios.length < 10) {
        e.preventDefault();
        alert('⚠️ La descripción debe tener al menos 10 caracteres.');
        comentariosTextarea.focus();
        return;
      }

      // Confirmación antes de enviar
      const tipoTexto = tipoComentario === 'reparacion' ? 'reporte de mantenimiento' : 'observación';
      const espacioTexto = espacioSelect.options[espacioSelect.selectedIndex].text;

      const confirmacion = confirm(
        `¿Está seguro de enviar este ${tipoTexto} para "${espacioTexto}"?\n\n` +
        `Descripción: ${comentarios.substring(0, 100)}${comentarios.length > 100 ? '...' : ''}`
      );

      if (!confirmacion) {
        e.preventDefault();
      }
    });

    // Inicializar estado
    togglePrioridad();

    // Auto-dismiss alerts después de 5 segundos
    setTimeout(function() {
      const alerts = document.querySelectorAll('.alert');
      alerts.forEach(alert => {
        if (alert.querySelector('.btn-close')) {
          alert.querySelector('.btn-close').click();
        }
      });
    }, 5000);
  }

  // Configurar previsualización de imágenes
  function configurarPreviewImagenes() {
    const inputImagenes = document.getElementById('imagenes');
    const previewContainer = document.getElementById('preview-imagenes');

    inputImagenes.addEventListener('change', function(e) {
      const files = Array.from(e.target.files);

      // Validar número máximo de archivos
      if (files.length > 4) {
        alert('⚠️ Máximo 4 imágenes permitidas');
        e.target.value = '';
        previewContainer.innerHTML = '';
        return;
      }

      // Limpiar preview anterior
      previewContainer.innerHTML = '';

      // Crear preview para cada imagen
      files.forEach((file, index) => {
        if (file.type.startsWith('image/')) {
          const reader = new FileReader();
          reader.onload = function(e) {
            const previewDiv = document.createElement('div');
            previewDiv.className = 'd-inline-block me-2 mb-2 position-relative';
            previewDiv.style.width = '100px';
            previewDiv.style.height = '100px';

            previewDiv.innerHTML = `
              <img src="${e.target.result}"
                   class="img-thumbnail"
                   style="width: 100%; height: 100%; object-fit: cover;">
              <button type="button"
                      class="btn btn-danger btn-sm position-absolute top-0 end-0"
                      style="padding: 2px 6px; font-size: 10px;"
                      onclick="eliminarImagenPreview(this, ${index})">
                <i class="fa fa-times"></i>
              </button>
            `;

            previewContainer.appendChild(previewDiv);
          };
          reader.readAsDataURL(file);
        }
      });
    });
  }

  // Función para eliminar imagen del preview
  function eliminarImagenPreview(button, index) {
    const inputImagenes = document.getElementById('imagenes');
    const previewContainer = document.getElementById('preview-imagenes');

    // Remover el preview
    button.parentElement.remove();

    // Crear nuevo FileList sin el archivo eliminado
    const dt = new DataTransfer();
    const files = Array.from(inputImagenes.files);

    files.forEach((file, i) => {
      if (i !== index) {
        dt.items.add(file);
      }
    });

    inputImagenes.files = dt.files;
  }

</script>


<!-- Vendor JS -->
<script th:src="@{/js/vendors.min.js}"></script>
<script th:src="@{/js/pages/chat-popup.js}"></script>
<script th:src="@{/assets/icons/feather-icons/feather.min.js}"></script>
<script th:src="@{/assets/vendor_components/c3/d3.min.js}"></script>
<script th:src="@{/assets/vendor_components/c3/c3.min.js}"></script>

<!-- Rhythm Admin App -->
<script th:src="@{/js/template.js}"></script>
<script th:src="@{/js/pages/c3-data.js}"></script>


</body>
</html>
