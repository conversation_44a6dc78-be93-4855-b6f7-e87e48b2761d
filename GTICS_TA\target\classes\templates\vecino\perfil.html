<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<meta name="description" content="">
	<meta name="author" content="">
	<link rel="icon" th:href="@{/images/san_miguel_logo.ico}">

	<title>Mi Perfil</title>

	<!-- Vendors Style-->
	<link rel="stylesheet" th:href="@{/css/vendors_css.css}">

	<!-- Style-->
	<link rel="stylesheet" th:href="@{/css/style.css}">
	<link rel="stylesheet" th:href="@{/css/skin_color.css}">

</head>

<body class="hold-transition light-skin sidebar-mini theme-success fixed">

<div class="wrapper">
	<div id="loader"></div>

	<header class="main-header">
		<div class="d-flex align-items-center logo-box justify-content-center">
			<!-- Logo -->
			<a href="/static/assets/vendor_components/jquery-validation-1.17.0/demo/requirejs/index.html" class="logo">
				<!-- logo-->
				<div class="logo-mini w-150 text-center">
					<span class="light-logo"><img th:src="@{/images/logo-sanMiguel.png}" alt="logo"></span>
				</div>
			</a>
		</div>
		<!-- Header Navbar -->
		<nav class="navbar navbar-static-top">
			<!-- Sidebar toggle button-->
			<div class="app-menu">
				<ul class="header-megamenu nav">
					<li class="btn-group nav-item">
						<a href="#" class="waves-effect waves-light nav-link push-btn btn-primary-light" data-toggle="push-menu" role="button">
							<i data-feather="align-left"></i>
						</a>
					</li>
				</ul>
			</div>

			<div th:replace="fragments :: navbarFragment"></div>
		</nav>
	</header>

	<!-- Left side column. contains the logo and sidebar -->
	<aside th:replace="fragments :: sidebarFragment"></aside>

	<!-- Content Wrapper. Contains page content -->
	<div class="content-wrapper">
		<div class="container-full">
			<!-- Content Header (Page header) -->
			<div class="content-header">
				<div class="d-flex align-items-center">
					<div class="me-auto">
						<h4 class="page-title">Perfil</h4>
					</div>

				</div>
			</div>

			<!-- Main content -->
			<section class="content">

				<div class="row">
					<div class="col-12 col-lg-7 col-xl-8">

						<div class="nav-tabs-custom">
							<ul class="nav nav-tabs">
								<li class="active"><a href="#editarPerfil" data-bs-toggle="tab">Editar perfil</a></li>
							</ul>

							<div class="tab-content">

								<!-- /.tab-pane -->

								<div class="tab-pane active show" id="editarPerfil">

									<div class="box no-shadow">
										<!-- Basic Forms -->
										<form th:action="@{/vecino/guardarperfil}" th:object="${usuario}" method="post" enctype="multipart/form-data">
											<div class="box-body">
												<h4 class="mt-0 mb-20" style="color: rgb(90, 201, 221);">1. Información personal:</h4>
												<input type="hidden" th:field="*{id}">
												<input type="hidden" th:field="*{contrasenia}">
												<input type="hidden" th:field="*{rol}">
												<div class="form-group">
													<label for="nombres" class="form-label">Nombres:</label>
													<input type="text" class="form-control" id="nombres" name="nombres" th:field="*{nombres}" readonly>
												</div>

												<div class="form-group">
													<label for="apellidos" class="form-label">Apellidos:</label>
													<input type="text" class="form-control" id="apellidos" name="apellidos" th:field="*{apellidos}" readonly>
												</div>

												<div class="form-group">
													<label for="dni" class="form-label">DNI:</label>
													<input type="number" class="form-control" id="dni" name="dni" th:field="*{dni}" readonly>
												</div>

												<div class="form-group">
													<label for="correo" class="form-label">Email:</label>
													<input type="email" class="form-control" name="correo" id="correo" th:field="*{correo}" readonly>
												</div>

												<div class="form-group">
													<label for="celular" class="form-label">Celular:</label>
													<input type="tel" class="form-control" id="celular" th:field="*{numCelular}" th:classappend="${#fields.hasErrors('numCelular')?'is-invalid':''}"/>
													<div class="invalid-feedback" th:if="${#fields.hasErrors('numCelular')}" th:errors="*{numCelular}"></div>
												</div>

												<div class="form-group">
													<label for="fechaNacimiento" class="form-label">Fecha de nacimiento:</label>
													<input class="form-control" type="date" id="fechaNacimiento" name="fechaNacimiento" th:field="*{fechaNacimiento}" readonly>
												</div>

												<div class="form-group">
													<label for="direccion" class="form-label">Dirección:</label>
													<input type="text" class="form-control" id="direccion" name="direccion" th:field="*{direccion}" readonly>
												</div>



												<hr>

												<h4 class="mt-0 mb-20" style="color: rgb(90, 201, 221);">2. Foto de perfil:</h4>
												<div class="mb-3">
													<label for="archivo" class="form-label">Adjunte su nueva foto</label>
													<input class="form-control" type="file" id="archivo" name="archivo" th:classappend="${msg != null ?'is-invalid':''}">
													<div class="invalid-feedback" th:if="${msg != null}" th:text="${msg}"></div>
												</div>

												<button type="submit" class="btn btn-primary">Actualizar</button>
											</div>
										</form>
									</div>
								</div>
							</div>
						</div>
					</div>

					<div class="col-12 col-lg-5 col-xl-4">
						<div class="box box-widget widget-user">
							<div class="widget-user-header bg-img bbsr-0 bber-0"
								 th:style="|background: url('@{/images/gallery/full/10.jpg}') center center;|"
								 data-overlay="5">
								<h3 class="widget-user-username text-white" th:text="${usuario.getNombres()} + ' ' + ${usuario.getApellidos()}"></h3>
								<h6 class="widget-user-desc text-white" th:text="'Usuario'"></h6>
							</div>
							<div class="widget-user-image">
								<img class="rounded-circle h-100 w-100" th:src="@{|profileimage/${usuario.getId()}|}"  alt="/images/avatar/avatar-1.png"
									 onerror="this.onerror=null;this.src='/images/avatar/avatar-1.png';">
							</div>

							<div class="box-footer">
								<div class="justify-content-center">
									<div id="dni-profile">
										<div class="description-block">
											<h5 class="description-header" th:text="${usuario.dni}"></h5>
											<span class="description-text">DNI</span>
										</div>
									</div>
								</div>
							</div>

						</div>
						<div class="box">
							<div class="box-body box-profile">
								<div class="row">
									<div class="col-12">
										<p>Email :<span class="text-gray ps-10" th:text="${usuario.correo}"></span></p>
										<p>Celular :<span class="text-gray ps-10" th:text="${usuario.numCelular}"></span></p>
										<p>Dirección :<span class="text-gray ps-10" th:text="${usuario.direccion}"></span></p>
									</div>
								</div>
							</div>
						</div>

					</div>

				</div>

			</section>

			<!-- /.content -->
		</div>
	</div>
	<!-- /.content-wrapper -->

	<footer class="main-footer">
		&copy; <script>document.write(new Date().getFullYear())</script> <a href="https://munisanmiguel.gob.pe/">Municipalidad de San Miguel</a>. Todos los derechos reservados.
	</footer>
	<!-- Control Sidebar -->
	<aside class="control-sidebar">

		<div class="rpanel-title"><span class="pull-right btn btn-circle btn-danger" data-toggle="control-sidebar"><i class="ion ion-close text-white"></i></span> </div>  <!-- Create the tabs -->
		<ul class="nav nav-tabs control-sidebar-tabs">
			<li class="nav-item"><a href="#control-sidebar-home-tab" data-bs-toggle="tab" class="active"><i class="mdi mdi-message-text"></i></a></li>
			<li class="nav-item"><a href="#control-sidebar-settings-tab" data-bs-toggle="tab"><i class="mdi mdi-playlist-check"></i></a></li>
		</ul>
		<!-- Tab panes -->
		<div class="tab-content">
			<!-- Home tab content -->
			<div class="tab-pane active" id="control-sidebar-home-tab">
				<div class="flexbox">
					<a href="javascript:void(0)" class="text-grey">
						<i class="ti-more"></i>
					</a>
					<p>Users</p>
					<a href="javascript:void(0)" class="text-end text-grey"><i class="ti-plus"></i></a>
				</div>
				<div class="lookup lookup-sm lookup-right d-none d-lg-block">
					<input type="text" name="s" placeholder="Search" class="w-p100">
				</div>
				<div class="media-list media-list-hover mt-20">
					<div class="media py-10 px-0">
						<a class="avatar avatar-lg status-success" href="#">
							<img src="../images/avatar/1.jpg" alt="...">
						</a>
						<div class="media-body">
							<p class="fs-16">
								<a class="hover-primary" href="#"><strong>Tyler</strong></a>
							</p>
							<p>Praesent tristique diam...</p>
							<span>Just now</span>
						</div>
					</div>

					<div class="media py-10 px-0">
						<a class="avatar avatar-lg status-danger" href="#">
							<img src="../images/avatar/2.jpg" alt="...">
						</a>
						<div class="media-body">
							<p class="fs-16">
								<a class="hover-primary" href="#"><strong>Luke</strong></a>
							</p>
							<p>Cras tempor diam ...</p>
							<span>33 min ago</span>
						</div>
					</div>

					<div class="media py-10 px-0">
						<a class="avatar avatar-lg status-warning" href="#">
							<img src="../images/avatar/3.jpg" alt="...">
						</a>
						<div class="media-body">
							<p class="fs-16">
								<a class="hover-primary" href="#"><strong>Evan</strong></a>
							</p>
							<p>In posuere tortor vel...</p>
							<span>42 min ago</span>
						</div>
					</div>

					<div class="media py-10 px-0">
						<a class="avatar avatar-lg status-primary" href="#">
							<img src="../images/avatar/4.jpg" alt="...">
						</a>
						<div class="media-body">
							<p class="fs-16">
								<a class="hover-primary" href="#"><strong>Evan</strong></a>
							</p>
							<p>In posuere tortor vel...</p>
							<span>42 min ago</span>
						</div>
					</div>

					<div class="media py-10 px-0">
						<a class="avatar avatar-lg status-success" href="#">
							<img src="../images/avatar/1.jpg" alt="...">
						</a>
						<div class="media-body">
							<p class="fs-16">
								<a class="hover-primary" href="#"><strong>Tyler</strong></a>
							</p>
							<p>Praesent tristique diam...</p>
							<span>Just now</span>
						</div>
					</div>

					<div class="media py-10 px-0">
						<a class="avatar avatar-lg status-danger" href="#">
							<img src="../images/avatar/2.jpg" alt="...">
						</a>
						<div class="media-body">
							<p class="fs-16">
								<a class="hover-primary" href="#"><strong>Luke</strong></a>
							</p>
							<p>Cras tempor diam ...</p>
							<span>33 min ago</span>
						</div>
					</div>

					<div class="media py-10 px-0">
						<a class="avatar avatar-lg status-warning" href="#">
							<img src="../images/avatar/3.jpg" alt="...">
						</a>
						<div class="media-body">
							<p class="fs-16">
								<a class="hover-primary" href="#"><strong>Evan</strong></a>
							</p>
							<p>In posuere tortor vel...</p>
							<span>42 min ago</span>
						</div>
					</div>

					<div class="media py-10 px-0">
						<a class="avatar avatar-lg status-primary" href="#">
							<img src="../images/avatar/4.jpg" alt="...">
						</a>
						<div class="media-body">
							<p class="fs-16">
								<a class="hover-primary" href="#"><strong>Evan</strong></a>
							</p>
							<p>In posuere tortor vel...</p>
							<span>42 min ago</span>
						</div>
					</div>

				</div>

			</div>
			<!-- /.tab-pane -->
			<!-- Settings tab content -->
			<div class="tab-pane" id="control-sidebar-settings-tab">
				<div class="flexbox">
					<a href="javascript:void(0)" class="text-grey">
						<i class="ti-more"></i>
					</a>
					<p>Todo List</p>
					<a href="javascript:void(0)" class="text-end text-grey"><i class="ti-plus"></i></a>
				</div>
				<ul class="todo-list mt-20">
					<li class="py-15 px-5 by-1">
						<!-- checkbox -->
						<input type="checkbox" id="basic_checkbox_1" class="filled-in">
						<label for="basic_checkbox_1" class="mb-0 h-15"></label>
						<!-- todo text -->
						<span class="text-line">Nulla vitae purus</span>
						<!-- Emphasis label -->
						<small class="badge bg-danger"><i class="fa fa-clock-o"></i> 2 mins</small>
						<!-- General tools such as edit or delete-->
						<div class="tools">
							<i class="fa fa-edit"></i>
							<i class="fa fa-trash-o"></i>
						</div>
					</li>
					<li class="py-15 px-5">
						<!-- checkbox -->
						<input type="checkbox" id="basic_checkbox_2" class="filled-in">
						<label for="basic_checkbox_2" class="mb-0 h-15"></label>
						<span class="text-line">Phasellus interdum</span>
						<small class="badge bg-info"><i class="fa fa-clock-o"></i> 4 hours</small>
						<div class="tools">
							<i class="fa fa-edit"></i>
							<i class="fa fa-trash-o"></i>
						</div>
					</li>
					<li class="py-15 px-5 by-1">
						<!-- checkbox -->
						<input type="checkbox" id="basic_checkbox_3" class="filled-in">
						<label for="basic_checkbox_3" class="mb-0 h-15"></label>
						<span class="text-line">Quisque sodales</span>
						<small class="badge bg-warning"><i class="fa fa-clock-o"></i> 1 day</small>
						<div class="tools">
							<i class="fa fa-edit"></i>
							<i class="fa fa-trash-o"></i>
						</div>
					</li>
					<li class="py-15 px-5">
						<!-- checkbox -->
						<input type="checkbox" id="basic_checkbox_4" class="filled-in">
						<label for="basic_checkbox_4" class="mb-0 h-15"></label>
						<span class="text-line">Proin nec mi porta</span>
						<small class="badge bg-success"><i class="fa fa-clock-o"></i> 3 days</small>
						<div class="tools">
							<i class="fa fa-edit"></i>
							<i class="fa fa-trash-o"></i>
						</div>
					</li>
					<li class="py-15 px-5 by-1">
						<!-- checkbox -->
						<input type="checkbox" id="basic_checkbox_5" class="filled-in">
						<label for="basic_checkbox_5" class="mb-0 h-15"></label>
						<span class="text-line">Maecenas scelerisque</span>
						<small class="badge bg-primary"><i class="fa fa-clock-o"></i> 1 week</small>
						<div class="tools">
							<i class="fa fa-edit"></i>
							<i class="fa fa-trash-o"></i>
						</div>
					</li>
					<li class="py-15 px-5">
						<!-- checkbox -->
						<input type="checkbox" id="basic_checkbox_6" class="filled-in">
						<label for="basic_checkbox_6" class="mb-0 h-15"></label>
						<span class="text-line">Vivamus nec orci</span>
						<small class="badge bg-info"><i class="fa fa-clock-o"></i> 1 month</small>
						<div class="tools">
							<i class="fa fa-edit"></i>
							<i class="fa fa-trash-o"></i>
						</div>
					</li>
					<li class="py-15 px-5 by-1">
						<!-- checkbox -->
						<input type="checkbox" id="basic_checkbox_7" class="filled-in">
						<label for="basic_checkbox_7" class="mb-0 h-15"></label>
						<!-- todo text -->
						<span class="text-line">Nulla vitae purus</span>
						<!-- Emphasis label -->
						<small class="badge bg-danger"><i class="fa fa-clock-o"></i> 2 mins</small>
						<!-- General tools such as edit or delete-->
						<div class="tools">
							<i class="fa fa-edit"></i>
							<i class="fa fa-trash-o"></i>
						</div>
					</li>
					<li class="py-15 px-5">
						<!-- checkbox -->
						<input type="checkbox" id="basic_checkbox_8" class="filled-in">
						<label for="basic_checkbox_8" class="mb-0 h-15"></label>
						<span class="text-line">Phasellus interdum</span>
						<small class="badge bg-info"><i class="fa fa-clock-o"></i> 4 hours</small>
						<div class="tools">
							<i class="fa fa-edit"></i>
							<i class="fa fa-trash-o"></i>
						</div>
					</li>
					<li class="py-15 px-5 by-1">
						<!-- checkbox -->
						<input type="checkbox" id="basic_checkbox_9" class="filled-in">
						<label for="basic_checkbox_9" class="mb-0 h-15"></label>
						<span class="text-line">Quisque sodales</span>
						<small class="badge bg-warning"><i class="fa fa-clock-o"></i> 1 day</small>
						<div class="tools">
							<i class="fa fa-edit"></i>
							<i class="fa fa-trash-o"></i>
						</div>
					</li>
					<li class="py-15 px-5">
						<!-- checkbox -->
						<input type="checkbox" id="basic_checkbox_10" class="filled-in">
						<label for="basic_checkbox_10" class="mb-0 h-15"></label>
						<span class="text-line">Proin nec mi porta</span>
						<small class="badge bg-success"><i class="fa fa-clock-o"></i> 3 days</small>
						<div class="tools">
							<i class="fa fa-edit"></i>
							<i class="fa fa-trash-o"></i>
						</div>
					</li>
				</ul>
			</div>
			<!-- /.tab-pane -->
		</div>
	</aside>
	<!-- /.control-sidebar -->

	<!-- Add the sidebar's background. This div must be placed immediately after the control sidebar -->
	<div class="control-sidebar-bg"></div>
</div>
<!-- ./wrapper -->

<!-- Page Content overlay -->

<!-- Script para habilitar/deshabilitar campos -->

<!-- Vendor JS -->
<script th:src="@{/js/vendors.min.js}"></script>
<script th:src="@{/js/pages/chat-popup.js}"></script>
<script th:src="@{/assets/icons/feather-icons/feather.min.js}"></script>
<script th:src="@{/assets/vendor_components/c3/d3.min.js}"></script> <script th:src="@{/assets/vendor_components/c3/c3.min.js}"></script>

<script th:src="@{/js/template.js}"></script>
<script th:src="@{/js/pages/c3-data.js}"></script>


</body>
</html>
