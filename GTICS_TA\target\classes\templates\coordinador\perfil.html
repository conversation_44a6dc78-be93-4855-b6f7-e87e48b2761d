<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="">
    <meta name="author" content="">
    <link rel="icon" th:href="@{/images/logo-solo.png}">

    <title>Perfil - Coordinador</title>

    <!-- Vendors Style-->
    <link th:href="@{/css/vendors_css.css}" rel="stylesheet">

    <!-- Style-->
    <link th:href="@{/css/style.css}" rel="stylesheet">
    <link th:href="@{/css/skin_color.css}" rel="stylesheet">

</head>

<body class="hold-transition light-skin sidebar-mini theme-success fixed">

<div class="wrapper">
    <div id="loader"></div>

    <header class="main-header">
        <div class="d-flex align-items-center logo-box justify-content-center">
            <!-- Logo -->
            <a th:href="@{/coordinador/principal}" class="logo">
                <!-- logo-->
                <div class="logo-mini w-150 text-center">
                    <span class="light-logo"><img th:src="@{/images/logo-sanMiguel.png}" alt="logo"></span>
                </div>
            </a>
        </div>
        <!-- Header Navbar -->
        <nav class="navbar navbar-static-top">
            <!-- Sidebar toggle button-->
            <div class="app-menu">
                <ul class="header-megamenu nav">
                    <li class="btn-group nav-item">
                        <a href="#" class="waves-effect waves-light nav-link push-btn btn-primary-light" data-toggle="push-menu" role="button">
                            <i data-feather="align-left"></i>
                        </a>
                    </li>

                </ul>
            </div>

            <div class="navbar-custom-menu r-side">
                <ul class="nav navbar-nav">
                    <!-- Notifications -->
                    <li class="dropdown notifications-menu">
                        <a href="#" class="waves-effect waves-light dropdown-toggle btn-info-light" data-bs-toggle="dropdown" title="Notifications">
                            <i data-feather="bell"></i>
                        </a>
                        <ul class="dropdown-menu animated bounceIn">

                            <li class="header">
                                <div class="p-20">
                                    <div class="flexbox">
                                        <div>
                                            <h4 class="mb-0 mt-0">Notificaciones</h4>
                                        </div>
                                        <div>
                                            <a href="#" class="text-danger">Clear All</a>
                                        </div>
                                    </div>
                                </div>
                            </li>

                            <li>
                                <!-- inner menu: contains the actual data -->
                                <ul class="menu sm-scrol">
                                    <li>
                                        <a href="#">
                                            <i class="fa fa-users text-info"></i> Horarios para semana santa
                                        </a>
                                    </li>
                                    <li>
                                        <a href="#">
                                            <i class="fa fa-warning text-warning"></i> Regulaciones nuevas para reserva de canchas de fútbol
                                        </a>
                                    </li>
                                    <li>
                                        <a href="#">
                                            <i class="fa fa-users text-danger"></i> Actualización de horarios
                                        </a>
                                    </li>
                                    <li>
                                        <a href="#">
                                            <i class="fa fa-shopping-cart text-success"></i> Días libres aprobados
                                        </a>
                                    </li>
                                </ul>
                            </li>
                            <li class="footer">
                                <a href="#">View all</a>
                            </li>
                        </ul>
                    </li>


                    <!-- User Account-->
                    <li>
                        <a href="perfil.html" class="waves-effect waves-light dropdown-toggle w-auto l-h-12 bg-transparent py-0 no-shadow" data-bs-toggle="dropdown" title="User">
                            <div class="d-flex pt-5">
                                <div class="text-end me-10">
                                    <p class="pt-5 fs-14 mb-0 fw-700 text-primary" th:text="${usuario.nombres + ' ' + usuario.apellidos}"></p>
                                    <small class="fs-10 mb-0 text-uppercase text-mute" th:text="${usuario.rol.nombre}"></small>
                                </div>
                                <img th:src="@{|/coordinador/profileimage/${usuario.getId()}?t=${#dates.format(#dates.createNow(), 'yyyyMMddHHmmss')}|}" class="avatar rounded-10 bg-primary-light h-40 w-40" alt=""
                                     onerror="this.onerror=null;this.src='/images/avatar/avatar-1.png';" />
                            </div>
                        </a>
                    </li>

                </ul>
            </div>
        </nav>
    </header>

    <!-- Left side column. contains the logo and sidebar -->
    <aside class="main-sidebar">
        <!-- sidebar-->
        <section class="sidebar position-relative">
            <div class="multinav">
                <div class="multinav-scroll" style="height: 100%;">
                    <!-- sidebar menu-->
                    <ul class="sidebar-menu" data-widget="tree">
                        <li>
                            <a th:href="@{/coordinador/perfil(id=3)}">
                                <i data-feather="user"></i>
                                <span>Perfil</span>
                            </a>
                        </li>
                        <li>
                            <a th:href="@{/coordinador/principal}">
                                <i data-feather="clock"></i>
                                <span>Marcar asistencia</span>
                            </a>
                        </li>
                        <!-- HORARIOS PARA COORDINADOR
                        <li>
                          <a href="horarios.html">
                            <i data-feather="calendar"></i>
                            <span>Horarios</span>
                          </a>
                        </li>-->
                        <li>
                            <a href="#" class="nav-link" onclick="document.getElementById('logoutForm').submit(); return false;">
                                <i data-feather="log-out"></i>
                                <span>Cerrar sesión</span>
                            </a>
                            <form id="logoutForm" th:action="@{/logout}" method="post" style="display: none;"></form>
                        </li>

                    </ul>

                    <div class="sidebar-widgets">
                        <div class="copyright text-center m-25">
                            <p><strong class="d-block">Municipalidad de San Miguel</strong> © <script>document.write(new Date().getFullYear())</script> All Rights Reserved</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </aside>

    <!-- Content Wrapper. Contains page content -->
    <div class="content-wrapper">
        <div class="container-full">
            <!-- Content Header (Page header) -->
            <div class="content-header">
                <div class="d-flex align-items-center">
                    <div class="me-auto">
                        <h4 class="page-title">Perfil</h4>
                        <div class="d-inline-block align-items-center">
                            <nav>
                                <ol class="breadcrumb">
                                    <li class="breadcrumb-item"><a href="#"><i class="mdi mdi-home-outline"></i></a></li>
                                    <li class="breadcrumb-item" aria-current="page">Menú</li>
                                </ol>
                            </nav>
                        </div>
                    </div>

                </div>
            </div>

            <!-- Main content -->
            <section class="content">

                <div class="row">
                    <div class="col-12 col-lg-7 col-xl-8">

                        <div class="nav-tabs-custom">
                            <ul class="nav nav-tabs">
                                <li><a class="active" href="#editarPerfil" data-bs-toggle="tab">Editar perfil</a></li>
                            </ul>

                            <div class="tab-content">
                                <!-- /.tab-pane -->

                                <div class="tab-pane active show" id="editarPerfil">

                                    <div class="box no-shadow">
                                        <!-- Basic Forms -->
                                        <form th:action="@{/coordinador/guardarperfil}" th:object="${usuario}" method="post" enctype="multipart/form-data">
                                            <div class="box-body">
                                                <h4 class="mt-0 mb-20" style="color: rgb(90, 201, 221);"> 1. Información personal:</h4>
                                                <input type="hidden" th:field="*{id}">
                                                <input type="hidden" th:field="*{contrasenia}">
                                                <input type="hidden" th:field="*{rol}">
                                                <div class="form-group">
                                                    <label class="form-label">Nombres:</label>
                                                    <input type="text" class="form-control" id="nombres" name="nombres" th:field="*{nombres}" readonly>
                                                </div>
                                                <div class="form-group">
                                                    <label class="form-label" for="apellidos">Apellidos:</label>
                                                    <input type="text" class="form-control" id="apellidos" name="apellidos" th:field="*{apellidos}" readonly>
                                                </div>
                                                <div class="form-group">
                                                    <label class="form-label">DNI:</label>
                                                    <input type="number" class="form-control" id="dni" name="dni" th:field="*{dni}" readonly>
                                                </div>
                                                <div class="form-group">
                                                    <label class="form-label">Email:</label>
                                                    <input type="email" class="form-control" name="correo" id="correo" th:field="*{correo}" readonly>
                                                </div>
                                                <div class="form-group">
                                                    <label for="celular" class="form-label">Celular:</label>
                                                    <input type="tel" class="form-control" id="celular" th:field="*{numCelular}" th:classappend="${#fields.hasErrors('numCelular')?'is-invalid':''}"/>
                                                    <div class="invalid-feedback" th:if="${#fields.hasErrors('numCelular')}" th:errors="*{numCelular}"></div>
                                                </div>
                                                <div class="form-group">
                                                    <label class="form-label">Fecha de nacimiento:</label>
                                                    <input class="form-control" type="date" id="fechaNacimiento" name="fechaNacimiento" th:field="*{fechaNacimiento}" readonly>
                                                </div>
                                                <div class="form-group">
                                                    <label class="form-label">Dirección:</label>
                                                    <input type="text" class="form-control" id="direccion" name="direccion" th:field="*{direccion}" readonly>
                                                </div>
                                                <hr>

                                                <h4 class="mt-0 mb-20" style="color: rgb(90, 201, 221)";>2. Foto de perfil:</h4>
                                                <div class="mb-3">
                                                    <label for="archivo" class="form-label">Adjunte su nueva foto</label>
                                                    <input class="form-control" type="file" id="archivo" name="archivo" th:classappend="${msg != null ?'is-invalid':''}">
                                                    <div class="invalid-feedback" th:if="${msg != null}" th:text="${msg}"></div>
                                                </div>
                                            </div>
                                            <!-- /.box-body -->
                                            <div class="box-footer">
                                                <button type="submit" class="btn btn-success pull-right">Actualizar</button>
                                            </div>
                                        </form>
                                        <!-- /.box -->
                                    </div>
                                </div>
                                <!-- /.tab-pane -->
                            </div>
                            <!-- /.tab-content -->
                        </div>
                        <!-- /.nav-tabs-custom -->
                    </div>
                    <!-- /.col -->

                    <div class="col-12 col-lg-5 col-xl-4">
                        <div class="box box-widget widget-user">
                            <!-- Add the bg color to the header using any of the bg-* classes -->
                            <div class="widget-user-header bg-img bbsr-0 bber-0" th:style="|background: url('@{/images/gallery/full/10.jpg}') center center;|" data-overlay="5">
                                <h3 class="widget-user-username text-white" th:text="${usuario.getNombres()} + ' ' + ${usuario.getApellidos()}" ></h3>
                                <h6 class="widget-user-desc text-white" th:text="${usuario.rol.nombre}"></h6>
                            </div>
                            <div class="widget-user-image">
                                <img class="rounded-circle h-100 w-100" th:src="@{|/coordinador/profileimage/${usuario.getId()}?t=${#dates.format(#dates.createNow(), 'yyyyMMddHHmmss')}|}" alt="User Avatar"
                                     onerror="this.onerror=null;this.src='/images/avatar/avatar-1.png';">
                            </div>
                            <div class="box-footer">
                                <div class="justify-content-center">
                                    <div id="dni-profile">
                                        <div class="description-block">
                                            <h5 class="description-header" th:text="${usuario.dni}"></h5>
                                            <span class="description-text">DNI</span>
                                        </div>
                                        <!-- /.description-block -->
                                    </div>
                                    <!-- /.col -->
                                </div>
                                <!-- /.row -->
                            </div>
                        </div>
                        <div class="box">
                            <div class="box-body box-profile">
                                <div class="row">
                                    <div class="col-12">
                                        <div>
                                            <p>Email :<span class="text-gray ps-10" th:text="${usuario.correo}"></span> </p>
                                            <p>Celular :<span class="text-gray ps-10" th:text="${usuario.numCelular}"></span></p>
                                            <p>Dirección :<span class="text-gray ps-10" th:text="${usuario.direccion}"></span></p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- /.box-body -->
                        </div>

                    </div>

                </div>
                <!-- /.row -->

            </section>
            <!-- /.content -->
        </div>
    </div>
    <!-- /.content-wrapper -->

    <footer class="main-footer">
        &copy; <script>document.write(new Date().getFullYear())</script> <a>New Fibra</a>. All Rights Reserved.
    </footer>
    <!-- Control Sidebar -->
    <aside class="control-sidebar">

        <div class="rpanel-title"><span class="pull-right btn btn-circle btn-danger" data-toggle="control-sidebar"><i class="ion ion-close text-white"></i></span> </div>  <!-- Create the tabs -->
        <ul class="nav nav-tabs control-sidebar-tabs">
            <li class="nav-item"><a href="#control-sidebar-home-tab" data-bs-toggle="tab" class="active"><i class="mdi mdi-message-text"></i></a></li>
            <li class="nav-item"><a href="#control-sidebar-settings-tab" data-bs-toggle="tab"><i class="mdi mdi-playlist-check"></i></a></li>
        </ul>
    </aside>
    <!-- /.control-sidebar -->

    <!-- Add the sidebar's background. This div must be placed immediately after the control sidebar -->
    <div class="control-sidebar-bg"></div>
</div>
<!-- ./wrapper -->

<!-- Page Content overlay -->


<!-- Vendor JS -->
<script th:src="@{/js/vendors.min.js}"></script>
<script th:src="@{/js/pages/chat-popup.js}"></script>
<script th:src="@{/assets/icons/feather-icons/feather.min.js}"></script>
<script th:src="@{/assets/vendor_components/c3/d3.min.js}"></script>
<script th:src="@{/assets/vendor_components/c3/c3.min.js}"></script>

<!-- Rhythm Admin App -->
<script th:src="@{/js/template.js}"></script>
<script th:src="@{/js/pages/c3-data.js}"></script>


</body>
</html>
