//[Dashboard Javascript]

//Project:	Rhythm Admin - Responsive Admin Template
//Primary use:   Used only for the main dashboard (index.html)


$(function () {

    'use strict';

    // Primer gráfico

    const topServicesEl = document.querySelector("#top_services");
    if (topServicesEl) {
        var chart = new ApexCharts(topServicesEl, options);
        chart.render();
    }




    var options = {
        series: [
            {
                name: "Outcome",
                data: [122, 222, 142, 182]
            }
        ],
        chart: {
            height: 100,
            width: 150,
            type: 'area',
            toolbar: {
                show: false
            }
        },
        colors: ['#77B6EA'],
        dataLabels: {
            enabled: false,
        },
        stroke: {
            curve: 'smooth',
            width: 1,
        },
        grid: {
            show: false,
        },
        yaxis: {
            labels: {
                show: false
            },
            axisTicks: {
                show: false
            }
        },
        xaxis: {
            categories: ['Abril', 'Mayo', 'Julio'],
            labels: {
                show: false
            },
            axisTicks: {
                show: false
            }
        },
        legend: {
            show: false,
        }
    };

    var chart = new ApexCharts(document.querySelector("#balance1"), options);
    chart.render();



    var options = {
        series: [
            {
                name: "Outcome",
                data: [102, 124, 92, 85]
            }
        ],
        chart: {
            height: 100,
            width: 150,
            type: 'area',
            toolbar: {
                show: false
            }
        },
        colors: ['#ee3158'],
        dataLabels: {
            enabled: false,
        },
        stroke: {
            curve: 'smooth',
            width: 1,
        },
        grid: {
            show: false,
        },
        yaxis: {
            labels: {
                show: false
            },
            axisTicks: {
                show: false
            }
        },
        xaxis: {
            categories: ['Jan', 'Feb', 'Mar', 'Apr'],
            labels: {
                show: false
            },
            axisTicks: {
                show: false
            }
        },
        legend: {
            show: false,
        }
    };

    var chart = new ApexCharts(document.querySelector("#balance2"), options);
    chart.render();




    var options = {
        series: [
            {
                name: "Discharge Patient",
                data: [12, 22, 14, 18, 22 , 13, 17]
            },
            {
                name: "Admit Patient",
                data: [28, 39, 23, 36, 45, 32, 43]
            }
        ],
        chart: {
            height: 275,
            type: 'line',
            dropShadow: {
                enabled: true,
                color: '#000',
                top: 18,
                left: 7,
                blur: 10,
                opacity: 0.2
            },
            toolbar: {
                show: false
            }
        },
        colors: ['#ee3158', '#1dbfc1'],
        dataLabels: {
            enabled: false,
        },
        stroke: {
            curve: 'smooth'
        },
        grid: {
            borderColor: '#e7e7e7',
        },
        xaxis: {
            categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul'],
        },
        legend: {
            show: false,
        }
    };

    var chart = new ApexCharts(document.querySelector("#overview_trend"), options);
    chart.render();
    a

    // Slim scrolling

    $('.inner-user-div').slimScroll({
        height: '283px'
    });

    $('.inner-user-div2').slimScroll({
        height: '298px'
    });

    $('.inner-user-div3').slimScroll({
        height: '200px'
    });

    $('.inner-user-div4').slimScroll({
        height: '432px'
    });

    var datepaginator = function() {
        return {
            init: function() {
                $("#paginator1").datepaginator()
            }
        }
    }();
    jQuery(document).ready(function() {
        datepaginator.init()
    });
});