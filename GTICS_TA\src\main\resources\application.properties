spring.application.name=GTICS_TA
spring.datasource.url=***********************************
spring.datasource.username=root
spring.datasource.password=root
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQL8Dialect
spring.jpa.hibernate.naming.physical-strategy=org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
spring.jpa.open-in-view=false
spring.thymeleaf.suffix=.html
spring.thymeleaf.mode=HTML
spring.thymeleaf.encoding=UTF-8
spring.thymeleaf.servlet.content-type=text/html
spring.thymeleaf.cache=false
spring.thymeleaf.prefix=classpath:/templates/

# Configuración de Google Maps
# Para desarrollo: usar la API key de prueba
# Para producción: la empresa debe reemplazar con su API key
google.maps.api.key=${GOOGLE_MAPS_API_KEY:AIzaSyB8gglF9Vdl-dlam3umZipOmq92nPlCEng}
google.maps.enabled=${GOOGLE_MAPS_ENABLED:true}
spring.thymeleaf.check-template-location=true
spring.thymeleaf.enabled=true
spring.thymeleaf.template-resolver-order=1
spring.session.store-type=jdbc
spring.session.jdbc.initialize-schema=always
spring.mail.host=smtp.gmail.com
spring.mail.port=587
spring.mail.username=<EMAIL>
spring.mail.password=cgxu gnnj hhus tuhy
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true

# Configuración de logging para debug
logging.level.org.springframework.security=DEBUG
logging.level.org.springframework.security.web=DEBUG
logging.level.org.springframework.security.authentication=DEBUG
logging.level.com.example.gtics_ta=DEBUG
logging.level.org.springframework.jdbc=DEBUG
#
aws.access.key.id=********************
aws.secret.access.key=hc+DPaBTYvZQZ3cpSRtFTeuTjVKNE+5jpJSLorlH
aws.session.token=IQoJb3JpZ2luX2VjEIT//////////wEaCXVzLXdlc3QtMiJHMEUCIQC7O9fBb9qRgdPfofwSFj62eRTMphYjAH3UJH5390XgGwIgMCTUH0d7bMZdroDhR25AsTZnAvkRPRXZXpn5pObWed0qqAIIbRAAGgw3MzAzMzU1MTMxODciDKcFJNHkT8fqJwoX6CqFAldxhX6NsB+AshimaBA8wgX8GDFK787NQuama7AgjdJv9ThmpTFwk8n8z8p2QE5YxbKN01zysDyiUO7CaK2c5Izca0f+PrjZaz560BdI+xNnNugb4pQEVCNt6I78wCY4ejVUpSi7ZM5p/q9QWDkj5EvOLjyiUX/BZlQ37iVcqajQYPBNOJP3H4BzumCUl/+Z5Z2t68U063NY673le1Amt717pnT/tk8i/N8mBbiV9DXmpLT2BDn1badELvZ8cd0y31c5dXxiWerFWozQu7MmTVSSn7xFShLdqZXNF1gY/z982Kp4F4E/M+4LObGYRaLMSGoUn6upVNd1X2mQHhZPH7OHqzh2qjCaz8PCBjqdAZ/Qj3ylhLm2s03b3l3iFDcAk1Q6RkQyhvAvBQQp8Cu40hUYxufiNdxjajkSEmNyYvLT7ztjDBLSWOVX98gaqdkysdeIKl/M3b0MPWg1CJED6F6GCU2oeWEBBdUqSWKbGHslRKy4BkkGVXi7/Vl5vnZSz1OTbFYbJRbB8gBSU5PzDIB+Q4qwrDeOWBw24wWwT+TAjN9h9cCPowVxv6g= 
aws.s3.region=us-east-1
aws.s3.bucket.name=gtics-images-bucket-2025
aws.s3.url.expiration.minutes=60