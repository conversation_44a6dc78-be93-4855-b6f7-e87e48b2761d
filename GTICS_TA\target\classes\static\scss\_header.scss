/*---Header---*/
.page-header{
    margin: 10px 0 20px;
	padding-bottom: 9px;
    border-bottom: 1px solid lighten($black, 90%);
    >small{
        color: lighten($black, 45%);
        display: block;
        margin-top: 5px;
    }
}

.main-header{
    div.logo-box{        
        width: $sid-bar-w;
        float: left;        
        -webkit-transition: -webkit-transform .3s ease-in-out, width .3s ease-in-out;
        -moz-transition: -moz-transform .3s ease-in-out, width .3s ease-in-out;
        -o-transition: -o-transform .3s ease-in-out, width .3s ease-in-out;
        transition: transform .3s ease-in-out, width .3s ease-in-out;
        background: $wrapper;
        border-top-right-radius: 0px;
    }
    .push-btn, .full-screen, .svg-bt-icon{
        height: 45px;
        width: 45px;
        padding: 10px;
        text-align: center;
        line-height: 20px;
        font-size: $fs-24;
        margin: 0 5px;
        background-color: $white;
        border-radius: $default-border-radius;
        -webkit-box-shadow: 0 3px 5px 1px rgba(0,0,0,.05);
        box-shadow: 0 3px 5px 1px rgba(0,0,0,.05); 
        @include hover-state{
            background-color: rgba($black, 0.03);
            color: $primary;
        }
    }  
    .push-btn img, .full-screen img, .svg-bt-icon img{
        height: 20px;
        width: 20px;    
        margin-top: 2.5px;
    }
    .logo-box{
        border-right: 1px solid rgba($dx-blue,0);
    }
    .app-menu{        
        .search-bx {   
            max-width: 250px;            
            -webkit-box-shadow: 0 3px 5px 1px rgba(0,0,0,.05);
            box-shadow: 0 3px 5px 1px rgba(0,0,0,.05);
            border-radius: $default-border-radius;
            overflow: hidden;
          input[type="search"] {
            background-color: $white;
            color: $dark;
            font-size: 16px;
            padding-left: 20px;
            border: none;
            border-radius: 5px 0px 0px 5px;
            height: 45px;
          }
          ::placeholder {
            color: #717C99;
          }
          .input-group{
             margin-top: 0px;
          }
          .input-group-append {
            margin-left: 0px;
          }
          .btn {
            background-color: $white;
            color: $icon-lite-color;
            font-weight: 700;
            font-size: 18px;
            border-radius: 0 5px 5px 0;
            padding: 3px 15px 3px 5px;
            box-shadow: none;
            img {
              width: 20px;
              margin-top: -3px;
              filter: invert(0.6) sepia(1) saturate(1) hue-rotate(185deg);
            }
          }
        }
    }
    max-height: $hed-max;
    z-index: 999;
    position: relative;
    .r-side {
        .app-menu, .btn-group a{ 
            margin: 18px 5px;
        }
    }
    .navbar{
        @include transition(margin-left .3s ease-in-out);
        margin-bottom: 0;
        margin-left: $sid-bar-w;
        min-height: $main-hed-nav;
        border-radius: 0;
        border-bottom: 0px solid rgba($dx-blue,0.16);
        padding: 0 $default-gutter-width;
        box-shadow: none;
        .nav{
            >li{
            &.user{
                >a{
                    >i{
                        margin-right: 5px;
                    }
                }
            }
            }
        }
    }
    .logo{
        @include transition(width .3s ease-in-out);            
        display: block;
        height: $main-hed-nav;
        font-size: 1.2rem;
        line-height: $main-hed-nav;
        text-align: left;
        padding: 0 15px;
        position: relative;
        z-index: 999;
        .logo-lg{
            line-height: $main-hed-nav;
	        display: inline-block;
	        padding-left: 5px;
            .dark-logo{
                display: none;
            }
            .light-logo{
                display: block;
            }
        }
        .logo-mini{
            display: inline-block;
            .dark-logo{
                display: none;
            }
            .light-logo{
                display: block;
            }
        }
    }
    #navbar-search-input{
    &.form-control {
        background-color: rgba($white, 0.2);
        border-color: transparent;
        @include active-focus-state{
            border-color: rgba($black, 0.1);
            background: rgba($white, 0.9);   
        }
        @include placeholder{
            color: lighten($black, 80%);
            opacity: 1;  
        }
    }  
    }
    .navbar-custom-menu{
        float: right;
        &.r-side{
            li.dropdown{
                .dropdown-toggle{
                    i{
                        &::after{
                            content: '';
                            position: absolute;
                            top: 23px;
                            right: 16px;
                            display: none;
                            width: 4px;
                            height: 4px;
                            border-radius: 100%;
                            background-color: $danger;
                            animation: beat .25s infinite alternate;
                            transform-origin: center;
                        }
                        &.animated{
                            &::after{
                                top: 17px;
                                right: -5px;
                            }
                        }
                        &.fa{
                            &.animated{
                                &::after{
                                    top: 17px;
                                    right: -5px;
                                }
                            }
                        }
                    }
                }
            } 
        }
    }
    .navbar-right{
        float: right;
        padding-right: 15px;        
    }
    .navbar-brand{
        color: $white;
    }
    .sidebar-toggle{
        @include hover-focus-state{
            color: $white;   
        }
        float: left;
        padding: 0 10px;
        font-size: 1.2rem;
        line-height: normal;
        .icon-bar {
            display: none;
        }
    }
}
.dark-skin{
    .main-header{
        .logo{
            .logo-lg{
                .dark-logo{
                    display: block;
                }
                .light-logo{
                    display: none;
                }
            }
        }
    }
}
@keyframes beat{
	to { transform: scale(2); }
}
.layout-top-nav {
    .main-header{
        .navbar{
            margin-left: 0;
        }
    } 
}

@include screen-sm-max {
    .main-header{
        position: relative;
	    z-index: 999;
        .navbar-right {            
            padding-right: 0px;
            float: none;
            >li{
                color: inherit;
                border: 0; 
            }
        }
        .logo{
            width: $p100;
            float: none;
	        text-align: center;
        }
        .navbar{
            width: $p100;
            float: none;
            margin: 0;
	        z-index: 999;
        }
        .navbar-custom-menu{
            float: right;
            padding-right: 0px;
        }
    }
    .navbar-collapse{
        .main-header{
            .navbar-right{
                margin: 7.5px -15px;
            }
        }
    }
    .navbar-custom-menu{
        .navbar-nav{
            li{
                >a{
                    &.dropdown-toggle{
                        padding: 0px 14px;
                    }
                }
            }
            >li{                
                &:last-child{
                    >a{
                      padding: 0px 14px;  
                    }
                }
            }
        }
    }
}

@include screen-md-max {
    .navbar-collapse{
        .pull-left {
            float: none!important;
            +.navbar-custom-menu{                
                display: block;
                position: absolute;
                top: 0;
                right: 40px;
            }
        }
    }
}
@include screen-md {
    .main-header {
        .logo {
            display: flex;
        }
    }
    .sidebar-mini{
        &.sidebar-collapse{
            .main-header{
                .logo-box{
                    width: $mini-sid-bar-w;
                    -webkit-transition: -webkit-transform .3s ease-in-out, width .3s ease-in-out;
                    -moz-transition: -moz-transform .3s ease-in-out, width .3s ease-in-out;
                    -o-transition: -o-transform .3s ease-in-out, width .3s ease-in-out;
                    transition: transform .3s ease-in-out, width .3s ease-in-out;
                }
                .logo{
	                text-align: center;
                    padding: 0;
                    margin: 0 auto;
                    >.logo-mini{
                        display: inline-block;
                    }
                    >.logo-lg {
                        display: none;
                    }
                }
                .navbar{
                    margin-left: $mini-sid-bar-w;
                }
            }
        }
    }
}
@include screen-sm-max {
    .main-header{
        > div{
            width: 100% !important;
            float: none;
            margin-left: 0 !important;
            .logo{                
                line-height: normal;
                height: auto;
                .logo-lg{
                    line-height: 50px;
                    padding-left: 0;
                }
            }
        }
        .navbar-custom-menu {
            .navbar-nav{
                .search-bar > div{
                    padding: 7px 0 8px;
                }
                > li{
                    height: 50px;
                    > a.dropdown-toggle{
                        margin: 3px 5px;
                    }
                    &:last-child > a{
                        margin: 2px 5px;
                    }
                    &.user {
                        a{
                            
                        }
                    }
                }
            }                     
            .full-screen{
                margin: 3px 5px !important;
                padding: 10px;
            }
        } 
    } 
}
@include screen-small {
    .main-header{
        .navbar-custom-menu {
            .navbar-nav{
                .search-bar > div{
                    padding: 10px 0 10px;
                }
                > li{
                    height: 50px;
                    > a.dropdown-toggle{
                        margin: 8px 5px;
                    }
                    &:last-child > a{
                        margin: 8px 5px;
                    }
                    &.user {
                        a{
                            
                        }
                    }
                }
            } 
        } 
    } 
}
.navbar-custom-menu{
    .navbar-nav {
        -ms-flex-direction: row;
        flex-direction: row;
        >li{            
	       height: $main-hed-nav;
            > a{
                &.dropdown-toggle{                    
                    padding: 10px;
                    vertical-align: middle;
                    display: block;
                    font-size: $fs-24;
                    height: 45px;
                    text-align: center;
                    width: 45px;
                    line-height: 20px;
                    margin: 18px 5px;        
                    -webkit-box-shadow: 0 3px 5px 1px rgba(0,0,0,.05);
                    box-shadow: 0 3px 5px 1px rgba(0,0,0,.05);
                    border-radius: $default-border-radius;
                    background-color: $white;
                }
            }
        }
        >li{
            &:last-child{
                >a{
                padding: 10px;
                vertical-align: middle;
                display: block;
                font-size: $fs-24;
                height: 45px;
                text-align: center;
                width: 45px;
                line-height: 20px;
                margin: 18px 5px;
                -webkit-box-shadow: 0 3px 5px 1px rgba(0,0,0,.05);
                box-shadow: 0 3px 5px 1px rgba(0,0,0,.05);
                border-radius: $default-border-radius;
                background-color: $white;
                }
            }  
        }
        >.search-box{
            >a{
                padding: 10px;
                vertical-align: middle;
                display: block;
                font-size: 1.5rem;
                border-radius: 100%;
                height: 45px;
                text-align: center;
                width: 45px;
                line-height: 25px;
                margin: 10px 5px;
            }
            > div{
                padding: 15px 0 15px;
            }
        }
        .full-screen-btn{
            >a{                
                padding: 10px;
                vertical-align: middle;
                display: block;
                font-size: 1.5rem;
                border-radius: 100%;
                height: 45px;
                text-align: center;
                width: 45px;
                line-height: 25px;
                margin: 10px 5px;
            }
        }
        .search-bar{
            > div{
                padding: 12px 0 25px;
            }
            .lookup::before{
                color: rgba($dark, 1);
                top: 42%;
            }
            .lookup-circle{
                input{                    
                    background-color: rgba($dark, 0.1);
                    height: 35px;
                    border-radius: $default-border-radius;
                }
            }
        }
    }
    >.navbar-nav{
        >li{
            position: relative;
            >.dropdown-menu{
                position: absolute;
                right: 0;
                left: auto; 
            }
        }
    }
    &.l-side{
        >.navbar-nav{
            >li{
                >.dropdown-menu{
                    right: auto;
                    left: 0;   
                } 
            } 
        } 
    }
    .dropdown-toggle::after {
        display: none;
    }    
}

@include screen-sm-max {
    .navbar-custom-menu{
        .navbar-nav{
            >.search-bar{
                .lookup input{                    
                    max-width: 110px;
                    padding-left: 10px !important;
                } 
            }
        }
    }
}
@include screen-small {
    .navbar-custom-menu{
        .navbar-nav{
            >.search-bar{
                .lookup input{                    
                    max-width: 50px;
                    padding-left: 10px !important;
	                padding: 0;
                    height: 30px;
                } 
            }
        }
    }
}

.search-box{
    .app-search{        
        position: relative;
        margin: 0px;
        display: block;
        z-index: 110;
        width: 100%;
        top: -50px;
        left: 0px;
        input {            
            width: 100.5%;
            padding: 0px 40px 0px 15px;
            border-radius: 50px;
            font-size: 14px;
            -webkit-transition: 0.5s ease-in;
            -o-transition: 0.5s ease-in;
            transition: 0.5s ease-in;
            border: none;
        }
        .srh-btn{           
            position: absolute;
            top: 9px;
            cursor: pointer;
            background: #ffffff;
            width: 15px;
            height: 15px;
            right: 15px;
            font-size: 14px; 
        }
    }
}
body{
    .main-header{
        .navbar{
            .nav{
                >li{
                    &.search-box {
                        >a{
                            @include hover-focus-state{
                                background: none;
                            }  
                        }
                    }
                }
            }
        }
    }
}

@include screen-tl{
    .main-header {
        .navbar-custom-menu{
            &.r-side{
                li.dropdown{
                    .dropdown-toggle{
                        i{
                            &::after{
                                right: auto;
                                top: 20px;
                            }  
                        }
                    }
                }
            }
        }
    } 
    .navbar-custom-menu{
        .navbar-nav {
            li{ 
                >a{
                    &.dropdown-toggle{
                        line-height: 25px;
                    }
                }
            }
            >li{
                &:last-child{
                    >a{
                        line-height: 25px;
                    }
                } 
            }
            .search-box{
                >a{
                    line-height: 25px;
                }
            }
        }
    }
}

.dropdown-menu{    
    box-shadow: none;
    border-color: lighten($black, 80%);
    &.scale-up{        
        box-shadow: 0 3px 12px rgba($black, .05);
        -webkit-box-shadow: 0 3px 12px rgba($black, .05);
        -moz-box-shadow: 0 3px 12px rgba($black, .05);
        border-color: rgba($black, .13);	
        display: inline-block;
        -webkit-transition: all .3s ease;
        transition: all .3s ease;
        -webkit-transform: scale(0);
        transform: scale(0);
        transform-origin: right 0;
    }
    >li{
        >a{
           color: lighten($black, 50%); 
            >i{
                margin-right: 10px;
            }
        }  
    }
    >.divider{
        background-color: lighten($black, 80%);
    }
}
.show{
    &>.dropdown-menu{
        &.scale-up{
            transform: scale(1);
            -webkit-transform: scale(1);
            transform-origin: left 0;
            -webkit-transform-origin: left 0;
        } 
    }
}
.main-header{
    .navbar-nav{
        >li{
            &.dropdown {
                >.dropdown-menu{
                    width: auto;
                    max-width: 250px;
                    padding: 0;
                    margin: 0;
                    top: 100%;
                    border: none;
                    >li{
                        position: relative;
                        &.header{
                            background-color: $white;
                            border-bottom: 1px solid $light;
                            color: $dark;
                            font-size: 14px;
                            border-radius: 4px 4px 0 0;
                            padding: 0;
                            >div{
                               box-shadow: none; 
                            }
                        }
                        &.footer{
                            >a{
                                font-size: 14px;
                                padding: 15px 10px;
                                text-align: center;
                                border-radius: 0 0 4px 4px;
                                line-height: 20px;
                                display: block; 
                                width: auto;
                                margin: 0;
                                &:hover{
                                    text-decoration: none;
                                    font-weight: 300;
                                }
                            }
                        }
                        .menu{
                            margin: 0;
                            padding: 0;
                            list-style: none;
                            overflow-x: hidden;
                            >li{
                                >a{
                                    display: block;
                                    white-space: nowrap;
                                    border-bottom: 1px solid $light;
                                    &:hover{
                                        background: $light;
                                        text-decoration: none; 
                                    }
                                    >i{
                                       width: 20px; 
                                    }
                                } 
                            }
                        }
                    }
                }
            }
        }
    }
}
.navbar-nav{
    >.notifications-menu{
        >.dropdown-menu{
            >li{
                .menu{
                    >li{
                        >a{
                            color: $dark;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            padding: 10px;  
                        }
                    }
                }
            }
        } 
    }
}

.mail-contnet{
    display: inline-block;
    width: 75%;
    vertical-align: middle;
    overflow: hidden;    
}
.navbar-nav{
    >.messages-menu{
        >.dropdown-menu{
            >li{
                .menu{
                    >li{
                        >a{
                            margin: 0;
                            padding: 15px;  
                            >div{
                                >img{
                                    margin: auto 10px auto auto;
                                    width: 40px;
                                    height: 40px;
                                }
                                >h4{
                                    padding: 0;
                                    margin: 0;
                                    color: $dark;
                                    font-size: 15px;
                                    position: relative;
                                    font-weight: 300;
                                    >small {
                                        color: lighten($black, 50%);
                                        font-size: 10px;
                                        position: absolute;
                                        top: 0;
                                        right: 0;
                                    }
                                }
                                >span {
                                    margin: 5px 0 0;
                                    font-size: 10px;
                                    color: lighten($black, 40%);
                                    overflow: hidden;
                                    text-overflow: ellipsis;
                                    white-space: nowrap;
                                    display: list-item;
                                }
                            }
                            >h4{
                                padding: 0;
                                margin: 0 0 0 45px;
                                color: $dark;
                                font-size: 15px;
                                position: relative; 
                                >small {
                                    color: lighten($black, 50%);
                                    font-size: 10px;
                                    position: absolute;
                                    top: 0;
                                    right: 0;
                                }
                            }
                            >p {
                                margin: 0 0 0 45px;
                                font-size: 12px;
                                color: lighten($black, 40%);
                            }
                            @include before-after-state{
                                content: " ";
                                display: table;
                            }
                        }
                    }
                }
            }
        } 
    }
    >.tasks-menu{
        >.dropdown-menu{
            >li{
                .menu{
                    >li{
                        >a{
                            padding: 10px;
                            >h3{
                                font-size: 14px;
                                padding: 0;
                                margin: 0 0 10px;
                                color: lighten($black, 50%); 
                            }
                            >.progress {
                                padding: 0;
                                margin: 0;
                            }
                        } 
                    } 
                }
            }
        }
    }
    >.user-menu{
        >.dropdown-menu{
            border-top-right-radius: 0;
            border-top-left-radius: 0;
            border-bottom-right-radius: 4px;
            border-bottom-left-radius: 4px;
            border-top-width: 0;
            width: auto;
            width: 250px;
            border: none;
            margin-top: 0; 
            >.user-body{
                border-bottom-right-radius: 4px;
                border-bottom-left-radius: 4px;
            }
            >li{
                &.user-header{
                    height: auto;
                    padding: 10px;
                    text-align: center;
                    img{
                        z-index: 5;
                        height: 70px;
                        width: 70px;
                        border: 3px solid transparent;
                        border-color: rgba($white, .2); 
                    }
                    .user-name{
                        z-index: 5;
                        color: $white;
                        margin-top: 0px;
                        margin-bottom: 0;
                        text-align: right;   
                    }
                    >p{
                        >small{
                            display: block;
                            font-size: 12px;     
                        }  
                    }
                }
            }
            >.user-body{
                @include before-after-state{
                    content: " ";
                    display: table;
                }
                a{
                    padding: 10px 15px;
                    display: block;
                    font-size: 1.1rem; 
                    i {
                        margin-right: 10px;
                    }
                }
                .divider{
                    height: 1px;
                    margin: 9px 0;
                    overflow: hidden;
                    background-color: rgba($dark, 0.13);
                }
            }
            .user-footer{
                padding: 10px 10px 0;
                border-bottom-left-radius: 5px;
                border-bottom-right-radius: 5px;
                @include before-after-state{
                    content: " ";
                    display: table;
                }
                .btn-default{
                    color: lighten($black, 50%);
                }
            }
        }
        .user-image{
            width: 25px;
            height: 25px;
            margin-top: 0px;    
        }
    }
}

@include screen-sm-max {
    .navbar-nav{
        >.user-menu{
            .user-image{
                float: none;
                margin-right: 0;
                margin-top: 0px;
                line-height: 10px; 
            }
        }
    }
    .navbar-custom-menu{
        .navbar-nav{
            li{
                >a{
                    &.dropdown-toggle{
                        padding: 10px;
                    }  
                } 
                &:last-child{
                    >a{
                        padding: 10px;
                    } 
                }
            }
            >.search-box{
                >a{
                    padding: 10px;  
                }
            }
        }
    }
}

@include screen-small {
    .navbar-custom-menu{
        .navbar-nav{
            li{
                >a{
                    &.dropdown-toggle{
                        font-size: 1rem;
                        height: 35px;
                        width: 35px;
                        line-height: normal;  
                    } 
                }
            }
            >li{
                &:last-child{
                    >a{
                        font-size: 1rem;
                        height: 35px;
                        width: 35px;
                        line-height: normal;  
                    }
                } 
            }
            >.search-box{
                >a{
                    font-size: 1rem;
                    height: 35px;
                    width: 35px;
                    line-height: normal;
                }
            }
        }
    }
    .navbar-nav{
        >.user-menu{
            .user-image{
                width: 15px;
                height: 15px; 
            }
        }
    }
}

.show:not(.dropup)>.animated-dropdown-menu{
    backface-visibility: visible!important;
    -webkit-animation: flipInX .7s both;
    -o-animation: flipInX .7s both;
    animation: flipInX .7s both;
}
@keyframes flipInX {
    0% {
    transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
    transition-timing-function: ease-in;
    opacity: 0;
}
40% {
    transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
    transition-timing-function: ease-in;
}
60% {
    transform: perspective(400px) rotate3d(1, 0, 0, 10deg);
    opacity: 1;
}
80% {
    transform: perspective(400px) rotate3d(1, 0, 0, -5deg);
}
100% {
    transform: perspective(400px);
}
}@-webkit-keyframes flipInX {
    0% {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
    -webkit-transition-timing-function: ease-in;
    opacity: 0;
}
40% {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
    -webkit-transition-timing-function: ease-in;
}
60% {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 10deg);
    opacity: 1;
}
80% {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -5deg);
}
100% {
    -webkit-transform: perspective(400px);
}
}
@include screen-md-max{
    .navbar-nav{
        >.user-menu{
            >.dropdown-menu{
                >.user-footer{
                    .btn-default{
                        &:hover{
                          background-color: $light;  
                        } 
                    }
                }
            }
        }
    }
    .navbar-custom-menu{
        >.navbar-nav{
            float: right;
            >li{
                position: static;
                >.dropdown-menu{
                    position: absolute;
                    right: 5%;
                    left: auto;
                }
            }
        }
        &.l-side{
            >.navbar-nav{
                >li{
                    >.dropdown-menu{
                        left: 5%;
                    }
                }
            } 
        }
    }
}

.app-menu{
    .dropdown-mega-menu {
        width: 53rem;
        padding: 0.5rem 1rem;
        .nav-item{
            &.nav-item-header {
                text-transform: none;
                font-size: $fs-20;
                padding-top: 0;
                font-weight: normal;
                padding: .715rem 1.072rem;
            }
        }
        .nav-link {
            display: flex;
            align-items: center;
            transition: background-color 0.3s ease, color 0.3s ease;
            cursor: pointer;
            color: $dark;
            border: none;
            font-weight: $fw-400;
            @include hover-focus-state{
                opacity: .8;
	            background-color: transparent !important;
            }
        }
    }
    .nav-link {
        .nav-link-icon {
            font-size: $fs-18;
            text-align: center;
            opacity: 1;
        }
        @include hover-focus-state{
            .nav-link-icon{
                opacity: .9;
            }
        }
    }
}

@include screen-tl{
    .app-menu{
        .dropdown-mega-menu {
            width: 40rem;
        }
    }
}

/*-----Preloader---*/

#loader {
    position: fixed;
    width: 100%;
    height: 100vh;
    z-index: 999999;
    overflow: visible;
    background: #fff url('../../images/preloaders/1.gif') no-repeat center center;
}























