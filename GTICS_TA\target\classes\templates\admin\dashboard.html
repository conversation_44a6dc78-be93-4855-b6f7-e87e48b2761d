<!DOCTYPE html>
<html lang="es" xmlns:th="http://www.thymeleaf.org">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta name="description" content="">
  <meta name="author" content="">
  <link rel="icon" href="/images/san_miguel_logo.ico">
  <link rel="icon" th:href="@{/images/san_miguel_logo.ico}">

  <title>Admin - Dashboard</title>

  <!-- Vendors Style-->
  <link rel="stylesheet" href="/css/vendors_css.css">
  <link rel="stylesheet" th:href="@{/css/vendors_css.css}">

  <!-- Style-->
  <link rel="stylesheet" href="/css/style.css">
  <link rel="stylesheet" href="/css/skin_color.css">
  <link rel="stylesheet" th:href="@{/css/style.css}">
  <link rel="stylesheet" th:href="@{/css/skin_color.css}">

  <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">




  <style>
    .box-equal-height .box-body {
      flex-grow: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
    }

    .box-equal-height {
      min-height: 390px; /* Ajusta según la altura total que necesites */
      display: flex;
      flex-direction: column;
      justify-content: space-between;
    }

    #patient_overview, #recent_trend {
      height: 220px !important;
    }

    #patient_overview {
      flex: 1;
      min-width: 200px;
    }

    .reporte-detalles {
      flex: 1;
      margin-left: 20px;
    }

    .notification-scroll {
      max-height: 280px; /* Ajusta según altura de tus otras tarjetas */
      overflow-y: auto;
    }


  </style>



</head>

<body class="hold-transition light-skin sidebar-mini theme-success fixed">

<div class="wrapper">
  <div id="loader"></div>

  <header class="main-header">

    <div class="d-flex align-items-center logo-box justify-content-center">
      <!-- Logo -->
      <a th:href="@{/admin}" class="logo">
        <!-- logo-->
        <div class="logo-mini w-150 text-center">
          <span class="light-logo"><img th:src="@{/images/logo-sanMiguel.png}" alt="logo"></span>
        </div>
      </a>
    </div>
    <!-- Header Navbar -->
    <nav class="navbar navbar-static-top">

      <!-- Sidebar toggle button-->
      <div class="app-menu">
        <ul class="header-megamenu nav">
          <li class="btn-group nav-item">
            <a href="#" class="waves-effect waves-light nav-link push-btn btn-primary-light" data-toggle="push-menu" role="button">
              <i data-feather="align-left"></i>
            </a>
          </li>

        </ul>
      </div>

      <div th:replace="fragments :: navbarAdmin"></div>
    </nav>
  </header>

  <aside class="main-sidebar">
    <!-- sidebar-->
    <section class="sidebar position-relative">
      <div class="multinav">
        <div class="multinav-scroll" style="height: 100%;">
          <!-- sidebar menu-->
          <ul class="sidebar-menu" data-widget="tree">
            <li>
              <a href="Admin_Dashboard.html">
                <i data-feather="monitor"></i>
                <span>Dashboard</span>
              </a>
            </li>

            <li>
              <a href="Admin_Reservas.html">
                <i data-feather="calendar"></i>
                <span>Reservas</span>
              </a>
            </li>

            <li>
              <a href="Admin_Servicios.html">
                <i data-feather="target"></i>
                <span>Servicios</span>
              </a>
            </li>

              <li>
                  <a href="#" class="nav-link" onclick="document.getElementById('logoutForm2').submit(); return false;">
                      <i data-feather="log-out"></i>
                      <span>Cerrar sesión</span>
                  </a>
                  <form id="logoutForm2" th:action="@{/logout}" method="post" style="display: none;"></form>
              </li>
          </ul>
        </div>
      </div>
    </section>
  </aside>
  <aside th:replace="fragments :: sideBarAdmin"></aside>


    <!-- Left side column. contains the logo and sidebar -->
    <div class="content-wrapper">
        <div class="container-full">
            <!-- Main content -->
            <section class="content">

                <div class="row">
                    <!-- Card 1 -->
                    <div class="col-md-3 col-sm-6 col-12 mb-3">
                        <div class="card shadow-sm border-0 text-center p-3 h-140" style="background-color: #D1FAE5;">
                            <div class="bg-primary-light rounded10 p-20 mx-auto w-100 h-100">
                                <img src="/images/svg-icon/medical/icono_1_d.svg" class="" alt="" />
                            </div>
                            <h6 class="text-dark">Total de Usuarios</h6>
                            <h2 class="text-success fw-bold" th:text="${dashboard.totalUsuarios}">0</h2>
                        </div>
                    </div>
                    <!-- Card 2 -->
                    <div class="col-md-3 col-sm-6 col-12 mb-3">
                        <div class="card shadow-sm border-0 text-center p-3 h-140" style="background-color: #FECACA;">
                            <div class="bg-danger-light rounded10 p-20 mx-auto w-100 h-100">
                                <img src="/images/svg-icon/medical/usuarios_baneados.svg" class="" alt="" />
                            </div>
                            <h6 class="text-dark">Total de Usuarios Baneados</h6>
                            <h2 class="text-success fw-bold" th:text="${dashboard.totalUsuariosBaneados}">0</h2>
                        </div>
                    </div>

                    <!-- Card 3 -->
                    <div class="col-md-3 col-sm-6 col-12 mb-3">
                        <div class="card shadow-sm border-0 text-center p-3 h-140" style="background-color: #BFDBFE;">
                            <div class="bg-warning-light rounded10 p-20 mx-auto w-100 h-100">
                                <img src="/images/svg-icon/medical/espacio_deportivo.svg" class="" alt="" />
                            </div>
                            <h6 class="text-dark">Cantidad de espacios </h6>
                            <h2 class="text-success fw-bold" th:text="${dashboard.espaciosDisponibles}">0</h2>
                        </div>
                    </div>

                    <div class="col-md-3 col-sm-6 col-12 mb-3">
                        <div class="card shadow-sm border-0 text-center p-3 h-140" style="background-color: #E0E7FF;">
                            <div class="bg-info-light rounded10 p-20 mx-auto w-100 h-100">
                                <img src="/images/svg-icon/medical/espacio_deportivo_ocupado.svg" class="" alt="" />
                            </div>
                            <h6 class="text-dark">Cantidad de reservas</h6>
                            <h2 class="text-success fw-bold" th:text="${dashboard.cantidadTotalReservas}">0</h2>
                        </div>
                    </div>
                    <!-- NUEVOS GRÁFICOS: Top Usuarios y Distribución Horaria -->
                    <div class="row">
                        <!-- Gráfico: Top 10 Usuarios con más reservas -->
                        <div class="col-xl-6 col-12">
                            <div class="box">
                                <div class="box-header">
                                    <h4 class="box-title">Top 10 Usuarios con Más Reservas</h4>
                                </div>
                                <div class="box-body">
                                    <div id="top_usuarios_reservas"></div>
                                </div>
                            </div>
                        </div>

                        <!-- Gráfico: Distribución de reservas por hora -->
                        <div class="col-xl-6 col-12">
                            <div class="box">
                                <div class="box-header">
                                    <h4 class="box-title">Distribución de Reservas por Hora</h4>
                                </div>
                                <div class="box-body">
                                    <div id="distribucion_horaria_reservas"></div>
                                </div>
                            </div>
                        </div>
                    </div>




                    <!-- Segunda fila con el gráfico circular y las estadísticas de pacientes alineados -->
                    <div class="row">
                        <!-- Gráfico de barras horizontal: Top 10 Servicios Más Reservados -->
                        <div class="col-xl-6 col-12">
                            <div class="box">
                                <div class="box-header">
                                    <h4 class="box-title">Top 10 Servicios Más Reservados</h4>
                                </div>
                                <div class="box-body">
                                    <div id="top_services"></div>
                                </div>
                            </div>
                        </div>

                        <!-- Gráfico circular: Porcentaje de Servicios Reservados -->
                        <div class="col-xl-6 col-12">
                            <div class="box">
                                <div class="box-header">
                                    <h4 class="box-title">Porcentaje de Servicios Reservados</h4>
                                </div>
                                <div class="box-body">
                                    <div id="chart432"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div> <!-- cierre de div.container-full -->
        </div>
    </div><!-- cierre de div.content-wrapper -->


    <!-- /.content-wrapper -->
    <footer class="main-footer">
        <div class="pull-right d-none d-sm-inline-block">
            <ul class="nav nav-primary nav-dotted nav-dot-separated justify-content-center justify-content-md-end">


            </ul>
        </div>
    </footer>


    <script src="/js/vendors.min.js"></script>
    <script src="/js/pages/chat-popup.js"></script>
    <script src="/assets/icons/feather-icons/feather.min.js"></script>
    <script src="/assets/vendor_components/apexcharts-bundle/dist/apexcharts.js"></script>
    <script src="/assets/vendor_components/date-paginator/moment.min.js"></script>
    <script src="/assets/vendor_components/bootstrap-datepicker/dist/js/bootstrap-datepicker.min.js"></script>
    <script src="/assets/vendor_components/date-paginator/bootstrap-datepaginator.min.js"></script>

    <!-- Rhythm Admin App -->
    <script src="/js/template.js"></script>
    <script src="/js/pages/dashboard.js"></script>

    <script th:inline="javascript">
        document.addEventListener("DOMContentLoaded", function () {

            const nombresTop = /*[[${dashboard.nombresServiciosTop}]]*/ [];
            const cantidadesTop = /*[[${dashboard.cantidadReservasTop}]]*/ [];

            const topServicesChart = new ApexCharts(document.querySelector("#top_services"), {
                series: [{
                    name: 'Reservas',
                    data: cantidadesTop
                }],
                chart: {
                    type: 'bar',
                    height: Math.max(355, cantidadesTop.length * 10) // 45 px por barra
                },
                plotOptions: {
                    bar: {
                        horizontal: true,
                        endingShape: 'rounded'
                    }
                },
                xaxis: {
                    categories: nombresTop,
                    labels: {
                        style: {
                            fontSize: '13px',
                            fontFamily: 'inherit',
                            cssClass: 'apexcharts-xaxis-label'
                        }
                    }
                },
                colors: ['#00D0FF']
            });
            topServicesChart.render();

            const nombresPorcentaje = /*[[${dashboard.nombresServiciosPorcentaje}]]*/ [];
            const cantidadesPorcentaje = /*[[${dashboard.cantidadServiciosPorcentaje}]]*/ [];

            const porcentajeChart = new ApexCharts(document.querySelector("#chart432"), {
                series: cantidadesPorcentaje,
                chart: {
                    type: 'donut',
                    height: 350,
                    toolbar: {
                        show: true,
                        tools: {
                            download: true,
                            selection: false,
                            zoom: false,
                            zoomin: false,
                            zoomout: false,
                            pan: false,
                            reset: false
                        },
                        export: {
                            csv: {
                                filename: "porcentaje_servicios",
                                headerCategory: "Servicio",
                                headerValue: "Reservas"
                            },
                            svg: {
                                filename: "porcentaje_servicios"
                            },
                            png: {
                                filename: "porcentaje_servicios"
                            }
                        }
                    }
                },
                labels: nombresPorcentaje,
                plotOptions: {
                    pie: {
                        donut: {
                            size: '65%',
                            labels: {
                                show: true,
                                total: {
                                    show: true,
                                    label: 'Total',
                                    formatter: function (w) {
                                        return w.globals.seriesTotals.reduce((a, b) => a + b, 0) + " reservas";
                                    }
                                }
                            }
                        }
                    }
                },
                legend: {
                    position: 'right'
                },
                tooltip: {
                    y: {
                        formatter: function (value, { series }) {
                            const total = series.reduce((a, b) => a + b, 0);
                            return `${value} reservas (${((value / total) * 100).toFixed(1)}%)`;
                        }
                    }
                }
            });
            porcentajeChart.render();

        });

        // Top 10 Usuarios con más reservas
        const nombresUsuariosTop = /*[[${dashboard.nombresUsuariosTop}]]*/ [];
        const cantidadUsuariosTop = /*[[${dashboard.cantidadReservasUsuariosTop}]]*/ [];

        const chartUsuarios = new ApexCharts(document.querySelector("#top_usuarios_reservas"), {
            series: [{
                name: 'Reservas',
                data: cantidadUsuariosTop
            }],
            chart: {
                type: 'bar',
                height: Math.max(350, cantidadUsuariosTop.length * 40)
            },
            plotOptions: {
                bar: {
                    horizontal: false,
                    endingShape: 'rounded'
                }
            },
            xaxis: {
                categories: nombresUsuariosTop
            },
            colors: ['#1a66d5']
        });
        chartUsuarios.render();

        // Distribución de reservas por hora
        const horasReservas = /*[[${dashboard.horasReservas}]]*/ [];
        const cantidadReservasPorHora = /*[[${dashboard.cantidadReservasPorHora}]]*/ [];

        const chartHoras = new ApexCharts(document.querySelector("#distribucion_horaria_reservas"), {
            series: [{
                name: 'Reservas',
                data: cantidadReservasPorHora
            }],
            chart: {
                type: 'area',
                height: 350,
                toolbar: {
                    show: true,
                    tools: {
                        download: true,
                        selection: false,
                        zoom: false,
                        zoomin: false,
                        zoomout: false,
                        pan: false,
                        reset: false,
                    }
                }
            },
            fill: {
                type: 'solid',
                opacity: 0.4
            },
            stroke: {
                curve: 'smooth',
                width: 3
            },

            plotOptions: {
                bar: {
                    columnWidth: '50%',
                    endingShape: 'rounded'
                }
            },
            xaxis: {
                categories: horasReservas,
                title: {
                    text: 'Hora'
                }
            },
            colors: ['#EF4444']
        });
        chartHoras.render();

    </script>







<!-- Vendor JS -->
<script src="/js/vendors.min.js"></script>
<script src="/js/pages/chat-popup.js"></script>
<script src="/assets/icons/feather-icons/feather.min.js"></script>

<script src="/assets/vendor_components/datatable/datatables.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

<!-- Rhythm Admin App -->
<script src="/js/template.js"></script>


</body>
</html>
